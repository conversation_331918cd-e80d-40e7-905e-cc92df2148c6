import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js";import{r as y,o as k,e as i,f as d,j as m,u as p,q as w,l as c,i as t,t as a,F as g,s as u,p as b,g as h,v as A,P,m as B}from"./vendor-bzEMSiaZ.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const C={class:"flex items-center justify-between"},F={class:"flex mt-2","aria-label":"Breadcrumb"},V={class:"inline-flex items-center space-x-1 md:space-x-3"},M={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},N={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},D={class:"py-12"},S={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},T={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},$={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},j={class:"p-6"},z={class:"flex items-center"},E={class:"ml-4"},L={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},R={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},q={class:"p-6"},G={class:"flex items-center"},H={class:"ml-4"},I={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},J={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},O={class:"flex items-center"},Q={class:"ml-4"},U={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},W={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},dt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},it={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},lt={class:"px-6 py-4 whitespace-nowrap"},ot={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},nt={class:"text-sm text-gray-500 dark:text-gray-400"},xt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm text-gray-900 dark:text-gray-100"},gt={class:"px-6 py-4 whitespace-nowrap"},yt={class:"text-sm text-gray-900 dark:text-gray-100"},mt={class:"px-6 py-4 whitespace-nowrap"},pt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ut={class:"px-6 py-4 whitespace-nowrap"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ft={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},_t={key:0,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Ct={__name:"Payments",setup(vt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Payments",href:"/payments"}],x=y(!1),r=y([]),f=async()=>{x.value=!0;try{const l=await window.axios.get("/payments-list");r.value=l.data.data||l.data||[]}catch(l){console.error("Error fetching payments:",l),r.value=[]}finally{x.value=!1}},_=l=>({paid:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",failed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",refunded:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"})[l]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return k(()=>{f()}),(l,e)=>(d(),i(g,null,[m(p(w),{title:"Payment Management"}),m(v,null,{header:c(()=>[t("div",C,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Payment Management ",-1)),t("nav",F,[t("ol",V,[(d(),i(g,null,u(n,(s,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<n.length-1?(d(),A(p(P),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[B(a(s.title),1)]),_:2},1032,["href"])):(d(),i("span",M,a(s.title),1)),o<n.length-1?(d(),i("svg",N,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):h("",!0)])),64))])])])])]),default:c(()=>[t("div",D,[t("div",S,[t("div",T,[t("div",$,[t("div",j,[t("div",z,[e[3]||(e[3]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-dollar-sign text-2xl text-green-500"})],-1)),t("div",E,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Revenue",-1)),t("p",L," $"+a(Array.isArray(r.value)?r.value.reduce((s,o)=>s+(o.amount||0),0).toFixed(2):"0.00"),1)])])])]),t("div",R,[t("div",q,[t("div",G,[e[5]||(e[5]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-check-circle text-2xl text-green-600"})],-1)),t("div",H,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Paid",-1)),t("p",I,a(Array.isArray(r.value)?r.value.filter(s=>s.status==="paid").length:0),1)])])])]),t("div",J,[t("div",K,[t("div",O,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),t("div",Q,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),t("p",U,a(Array.isArray(r.value)?r.value.filter(s=>s.status==="pending").length:0),1)])])])]),t("div",W,[t("div",X,[t("div",Y,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-times-circle text-2xl text-red-500"})],-1)),t("div",Z,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Failed",-1)),t("p",tt,a(Array.isArray(r.value)?r.value.filter(s=>s.status==="failed").length:0),1)])])])])]),t("div",et,[t("div",st,[x.value?(d(),i("div",at,e[10]||(e[10]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(d(),i("div",rt,[t("table",dt,[e[12]||(e[12]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Transaction "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Amount "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Date "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",it,[(d(!0),i(g,null,u(Array.isArray(r.value)?r.value:[],s=>(d(),i("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",lt,[t("div",ot,a(s.transaction_id),1),t("div",nt,a(s.payment_method),1)]),t("td",xt,[t("div",ct,a(s.patient_name),1)]),t("td",gt,[t("div",yt,a(s.provider_name),1)]),t("td",mt,[t("div",pt," $"+a((s.amount||0).toFixed(2)),1)]),t("td",ut,[t("span",{class:b([_(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.status),3)]),t("td",ht,a(s.date),1),t("td",ft,[e[11]||(e[11]=t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View ",-1)),s.status==="paid"?(d(),i("button",_t," Refund ")):h("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{Ct as default};
