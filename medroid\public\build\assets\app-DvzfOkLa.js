const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AnonymousChat-CahAuGDU.js","assets/vendor-bzEMSiaZ.js","assets/MedroidLogo-c6q-kzU_.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/MedroidLogo-B4Zax8Ug.css","assets/AnonymousChat-Fde6JZe3.css","assets/AppointmentDetail-CAg7pRQU.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js","assets/Primitive-CNCx3Yln.js","assets/createLucideIcon-BJGbtoZV.js","assets/AppLayout-tn0RQdqM.css","assets/AppointmentEdit-CXmn9PZa.js","assets/AppointmentPayment-D37E9-cF.js","assets/Appointments-DKchwJwG.js","assets/Appointments-Hzs7eS-r.css","assets/Chat-B5XtqtZQ.js","assets/ChatInput-Cgkfazpi.js","assets/ChatInput-xDMF4y4H.css","assets/Chat-CUXxca-4.css","assets/ChatHistory-B0c0oMqg.js","assets/Chats-CHxS_ypN.js","assets/Dashboard-BAEvzYaL.js","assets/Dashboard_backup-BtNQou4n.js","assets/Dashboard_backup-rPoQq-Jj.css","assets/Discover-UFa-y1AQ.js","assets/Patients-MtmHRGYa.js","assets/Payments-BH44ZFK8.js","assets/Permissions-Cy4ECzDr.js","assets/Availability-DXKWb_g1.js","assets/Earnings-C3pYka6z.js","assets/Patients-8Si9fdbO.js","assets/Profile-B30X1TL5.js","assets/Schedule-DgSDWujX.js","assets/Services-B5Kpl8AF.js","assets/ProviderRegister-DvaAni_r.js","assets/InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js","assets/Providers-CscRCujf.js","assets/Services-BmGhIZR0.js","assets/Shop-CGmMld3F.js","assets/Users-Dp-bjuqZ.js","assets/Welcome-BN7zIj7v.js","assets/Welcome-BTji7cks.css","assets/ConfirmPassword-C8iA_VLC.js","assets/index-Oq2yf1P1.js","assets/Label.vue_vue_type_script_setup_true_lang-B-E--Xd1.js","assets/index-CLsNIGVi.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-CXnZV5tQ.js","assets/ForgotPassword-DBNhQmDh.js","assets/TextLink.vue_vue_type_script_setup_true_lang-EizaLhrD.js","assets/Register-oSXjChlO.js","assets/Register-D8GMDeNH.css","assets/ResetPassword-C7uAfAwo.js","assets/VerifyEmail-BBd8gsp6.js","assets/Appearance-ChPuX1s1.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-iCtUkjBY.js","assets/Layout.vue_vue_type_script_setup_true_lang-DO8ZLI03.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-CIC6xNGN.js","assets/useBodyScrollLock-D5pdoO8V.js","assets/Password-BHvs_6xN.js","assets/Profile-ruiG5Cz_.js"])))=>i.map(i=>d[i]);
import{r as h,o as R,c as f,w as y,a as l,L as S,b as D,k as I,h as V}from"./vendor-bzEMSiaZ.js";const w="modulepreload",x=function(e){return"/build/"+e},P={},t=function(r,n,a){let v=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),u=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));v=Promise.allSettled(n.map(c=>{if(c=x(c),c in P)return;P[c]=!0;const m=c.endsWith(".css"),o=m?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${o}`))return;const s=document.createElement("link");if(s.rel=m?"stylesheet":w,m||(s.as="script"),s.crossOrigin="",s.href=c,u&&s.setAttribute("nonce",u),document.head.appendChild(s),m)return new Promise((E,O)=>{s.addEventListener("load",E),s.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${c}`)))})}))}function p(i){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=i,window.dispatchEvent(u),!u.defaultPrevented)throw i}return v.then(i=>{for(const u of i||[])u.status==="rejected"&&p(u.reason);return r().catch(p)})};async function k(e,r){for(const n of Array.isArray(e)?e:[e]){const a=r[n];if(!(typeof a>"u"))return typeof a=="function"?a():a}throw new Error(`Page not found: ${e}`)}function g(e){if(!(typeof window>"u"))if(e==="system"){const n=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",n==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,n=365)=>{if(typeof document>"u")return;const a=n*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${a};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();g(e||"system")};function C(){var r;if(typeof window>"u")return;const e=L();g(e||"system"),(r=z())==null||r.addEventListener("change",$)}function X(){const e=h("system");R(()=>{const n=localStorage.getItem("appearance");n&&(e.value=n)});function r(n){e.value=n,localStorage.setItem("appearance",n),b("appearance",n),g(n)}return{appearance:e,updateAppearance:r}}const d={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},_=h("normal"),F=()=>{const e=localStorage.getItem("medroid-font-size");e&&d[e]&&(_.value=e)},j=e=>{localStorage.setItem("medroid-font-size",e)};F();function N(){const e=f(()=>{var o;return((o=d[_.value])==null?void 0:o.scale)||1}),r=f(()=>{var o;return((o=d[_.value])==null?void 0:o.name)||"Normal"}),n=f(()=>{var o;return((o=d[_.value])==null?void 0:o.description)||""}),a=f(()=>Object.entries(d).map(([o,s])=>({key:o,...s}))),v=f(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),p=o=>{d[o]&&(_.value=o,j(o),m())},i=()=>{const o=Object.keys(d),s=o.indexOf(_.value);s<o.length-1&&p(o[s+1])},u=()=>{const o=Object.keys(d),s=o.indexOf(_.value);s>0&&p(o[s-1])},c=()=>{p("normal")},m=()=>{const o=document.documentElement;Object.entries(v.value).forEach(([s,E])=>{o.style.setProperty(s,E)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${_.value}`)};return y(_,()=>{m()},{immediate:!0}),{currentFontSize:_,fontSizeScale:e,fontSizeName:r,fontSizeDescription:n,availableFontSizes:a,fontSizeStyles:v,setFontSize:p,increaseFontSize:i,decreaseFontSize:u,resetFontSize:c,applyFontSizeToDocument:m,FONT_SIZES:d}}const q="Laravel";l.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";l.defaults.withCredentials=!0;const T=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},A=T();A&&(l.defaults.headers.common["X-CSRF-TOKEN"]=A);l.interceptors.request.use(e=>{const r=T();return r&&(e.headers["X-CSRF-TOKEN"]=r),e},e=>Promise.reject(e));l.interceptors.response.use(e=>e,e=>{var r;return((r=e.response)==null?void 0:r.status)===419&&(console.warn("CSRF token mismatch detected, refreshing page..."),window.location.reload()),Promise.reject(e)});window.axios=l;S({title:e=>`${e} - ${q}`,resolve:e=>k(`./pages/${e}.vue`,Object.assign({"./pages/AnonymousChat.vue":()=>t(()=>import("./AnonymousChat-CahAuGDU.js"),__vite__mapDeps([0,1,2,3,4,5])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-CAg7pRQU.js"),__vite__mapDeps([6,7,1,2,3,4,8,9,10])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-CXmn9PZa.js"),__vite__mapDeps([11,1,7,2,3,4,8,9,10])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-D37E9-cF.js"),__vite__mapDeps([12,1,7,2,3,4,8,9,10])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-DKchwJwG.js"),__vite__mapDeps([13,7,1,2,3,4,8,9,10,14])),"./pages/Chat.vue":()=>t(()=>import("./Chat-B5XtqtZQ.js"),__vite__mapDeps([15,1,7,2,3,4,8,9,10,16,17,18])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-B0c0oMqg.js"),__vite__mapDeps([19,7,1,2,3,4,8,9,10])),"./pages/Chats.vue":()=>t(()=>import("./Chats-CHxS_ypN.js"),__vite__mapDeps([20,7,1,2,3,4,8,9,10])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-BAEvzYaL.js"),__vite__mapDeps([21,1,7,2,3,4,8,9,10])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-BtNQou4n.js"),__vite__mapDeps([22,1,7,2,3,4,8,9,10,23])),"./pages/Discover.vue":()=>t(()=>import("./Discover-UFa-y1AQ.js"),__vite__mapDeps([24,7,1,2,3,4,8,9,10])),"./pages/Patients.vue":()=>t(()=>import("./Patients-MtmHRGYa.js"),__vite__mapDeps([25,7,1,2,3,4,8,9,10])),"./pages/Payments.vue":()=>t(()=>import("./Payments-BH44ZFK8.js"),__vite__mapDeps([26,7,1,2,3,4,8,9,10])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-Cy4ECzDr.js"),__vite__mapDeps([27,7,1,2,3,4,8,9,10])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-DXKWb_g1.js"),__vite__mapDeps([28,1,7,2,3,4,8,9,10])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-C3pYka6z.js"),__vite__mapDeps([29,7,1,2,3,4,8,9,10])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-8Si9fdbO.js"),__vite__mapDeps([30,1,7,2,3,4,8,9,10])),"./pages/Provider/Profile.vue":()=>t(()=>import("./Profile-B30X1TL5.js"),__vite__mapDeps([31,1,7,2,3,4,8,9,10])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-DgSDWujX.js"),__vite__mapDeps([32,1,7,2,3,4,8,9,10])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-B5Kpl8AF.js"),__vite__mapDeps([33,7,1,2,3,4,8,9,10])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-DvaAni_r.js"),__vite__mapDeps([34,1,35])),"./pages/Providers.vue":()=>t(()=>import("./Providers-CscRCujf.js"),__vite__mapDeps([36,7,1,2,3,4,8,9,10])),"./pages/Services.vue":()=>t(()=>import("./Services-BmGhIZR0.js"),__vite__mapDeps([37,1,7,2,3,4,8,9,10])),"./pages/Shop.vue":()=>t(()=>import("./Shop-CGmMld3F.js"),__vite__mapDeps([38,1,7,2,3,4,8,9,10])),"./pages/Users.vue":()=>t(()=>import("./Users-Dp-bjuqZ.js"),__vite__mapDeps([39,1,7,2,3,4,8,9,10])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-BN7zIj7v.js"),__vite__mapDeps([40,1,16,3,17,41])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-C8iA_VLC.js"),__vite__mapDeps([42,1,35,43,8,44,45,46,9])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-DBNhQmDh.js"),__vite__mapDeps([47,1,35,48,43,8,44,45,46,9])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-oSXjChlO.js"),__vite__mapDeps([49,1,35,3,50])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-C7uAfAwo.js"),__vite__mapDeps([51,1,35,43,8,44,45,46,9])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-BBd8gsp6.js"),__vite__mapDeps([52,1,48,43,8,46,9])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-ChPuX1s1.js"),__vite__mapDeps([53,1,3,9,54,7,2,4,8,10,55,43,45,56])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-CIC6xNGN.js"),__vite__mapDeps([57,1,55,43,8,45,44,58])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-BHvs_6xN.js"),__vite__mapDeps([59,1,35,7,2,3,4,8,9,10,55,43,45,54,44])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-ruiG5Cz_.js"),__vite__mapDeps([60,1,54,35,43,8,58,45,44,9,7,2,3,4,10,55]))})),setup({el:e,App:r,props:n,plugin:a}){D({render:()=>V(r,n)}).use(a).use(I).mount(e)},progress:{color:"#4B5563"}});C();const{applyFontSizeToDocument:W}=N();W();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(let r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});export{X as a,N as u};
