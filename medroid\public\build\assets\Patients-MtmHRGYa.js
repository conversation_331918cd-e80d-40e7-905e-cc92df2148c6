import{_}from"./AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js";import{r as p,o as k,e as i,f as d,j as u,u as y,q as b,l as c,i as t,t as r,F as g,s as h,v as w,g as A,P,m as D}from"./vendor-bzEMSiaZ.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const M={class:"flex items-center justify-between"},B={class:"flex mt-2","aria-label":"Breadcrumb"},V={class:"inline-flex items-center space-x-1 md:space-x-3"},C={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},F={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},N={class:"py-12"},j={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},L={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},T={class:"p-6"},z={class:"flex items-center"},Y={class:"ml-4"},q={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},G={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},H={class:"p-6"},I={class:"flex items-center"},S={class:"ml-4"},$={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},J={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},O={class:"flex items-center"},Q={class:"ml-4"},R={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},U={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},W={class:"p-6"},X={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},lt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},it={class:"px-6 py-4 whitespace-nowrap"},ot={class:"flex items-center"},nt={class:"ml-4"},xt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ct={class:"text-sm text-gray-500 dark:text-gray-400"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-900 dark:text-gray-100"},pt={class:"text-sm text-gray-500 dark:text-gray-400"},ut={class:"px-6 py-4 whitespace-nowrap"},yt={class:"text-sm text-gray-900 dark:text-gray-100"},ht={class:"text-sm text-gray-500 dark:text-gray-400 capitalize"},ft={class:"px-6 py-4 whitespace-nowrap"},vt={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Bt={__name:"Patients",setup(kt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Patients",href:"/patients"}],x=p(!1),a=p([]),f=async()=>{x.value=!0;try{const o=await window.axios.get("/patients-list");a.value=o.data.data||o.data||[]}catch(o){console.error("Error fetching patients:",o),a.value=[]}finally{x.value=!1}},v=o=>{const e=new Date,s=new Date(o);let l=e.getFullYear()-s.getFullYear();const m=e.getMonth()-s.getMonth();return(m<0||m===0&&e.getDate()<s.getDate())&&l--,l};return k(()=>{f()}),(o,e)=>(d(),i(g,null,[u(y(b),{title:"Patient Management"}),u(_,null,{header:c(()=>[t("div",M,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Patient Management ",-1)),t("nav",B,[t("ol",V,[(d(),i(g,null,h(n,(s,l)=>t("li",{key:l,class:"inline-flex items-center"},[l<n.length-1?(d(),w(y(P),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:c(()=>[D(r(s.title),1)]),_:2},1032,["href"])):(d(),i("span",C,r(s.title),1)),l<n.length-1?(d(),i("svg",F,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):A("",!0)])),64))])])]),e[2]||(e[2]=t("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Patient ",-1))])]),default:c(()=>[t("div",N,[t("div",j,[t("div",E,[t("div",L,[t("div",T,[t("div",z,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-users text-2xl text-green-500"})],-1)),t("div",Y,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Patients",-1)),t("p",q,r(Array.isArray(a.value)?a.value.length:0),1)])])])]),t("div",G,[t("div",H,[t("div",I,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-check text-2xl text-blue-500"})],-1)),t("div",S,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Active Patients",-1)),t("p",$,r(Array.isArray(a.value)?a.value.filter(s=>s.status==="active").length:0),1)])])])]),t("div",J,[t("div",K,[t("div",O,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-calendar-check text-2xl text-purple-500"})],-1)),t("div",Q,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Appointments",-1)),t("p",R,r(Array.isArray(a.value)?a.value.reduce((s,l)=>s+(l.total_appointments||0),0):0),1)])])])]),t("div",U,[t("div",W,[t("div",X,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-chart-line text-2xl text-orange-500"})],-1)),t("div",Z,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Avg Appointments",-1)),t("p",tt,r(Array.isArray(a.value)&&a.value.length>0?Math.round(a.value.reduce((s,l)=>s+(l.total_appointments||0),0)/a.value.length):0),1)])])])])]),t("div",et,[t("div",st,[x.value?(d(),i("div",at,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(d(),i("div",rt,[t("table",lt,[e[14]||(e[14]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contact "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Age/Gender "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Appointments "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Visit "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",dt,[(d(!0),i(g,null,h(Array.isArray(a.value)?a.value:[],s=>(d(),i("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",it,[t("div",ot,[e[12]||(e[12]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center"},[t("i",{class:"fas fa-user text-green-600 dark:text-green-400"})])],-1)),t("div",nt,[t("div",xt,r(s.name),1),t("div",ct," ID: "+r(s.id),1)])])]),t("td",gt,[t("div",mt,r(s.email),1),t("div",pt,r(s.phone),1)]),t("td",ut,[t("div",yt,r(v(s.date_of_birth))+" years",1),t("div",ht,r(s.gender),1)]),t("td",ft,[t("span",vt,r(s.total_appointments),1)]),t("td",_t,r(s.last_appointment),1),e[13]||(e[13]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},[t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View "),t("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"}," Edit "),t("button",{class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"}," History ")],-1))]))),128))])])]))])])])])]),_:1})],64))}};export{Bt as default};
