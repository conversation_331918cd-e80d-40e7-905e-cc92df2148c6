import{_ as C}from"./AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js";import{r as y,o as M,e as n,f as o,j as l,u as d,q as A,l as c,i as e,P as p,t as u,F as v,s as j,v as B,a as H}from"./vendor-bzEMSiaZ.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const L={class:"py-12"},D={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},N={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},F={class:"p-6 bg-white border-b border-gray-200"},S={class:"flex items-center justify-between"},z={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},E={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},I={class:"text-center py-8"},T={class:"mt-1 text-sm text-gray-500"},V={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},$={class:"p-6"},P={class:"space-y-4"},Y={class:"flex items-start justify-between"},q={class:"flex-1"},G={class:"text-lg font-medium text-gray-900 mb-1"},J={class:"flex items-center space-x-4 text-sm text-gray-500"},K={key:3,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},O={class:"text-center py-8"},et={__name:"ChatHistory",setup(Q){const w=[{title:"Chat History",href:"/chat-history"}],h=y([]),m=y(!1),i=y(null),x=async()=>{m.value=!0,i.value=null;try{const t=(await H.get("/api/chat/history")).data;let s=[];Array.isArray(t)?s=t:t.conversations&&Array.isArray(t.conversations)?s=t.conversations:t.data&&Array.isArray(t.data)&&(s=t.data);const a=s.filter(g=>!g.messages||!Array.isArray(g.messages)?!1:g.messages.some(f=>f&&f.content&&f.content.trim().length>0));h.value=a}catch(r){console.error("Error fetching chat history:",r),i.value="Failed to load chat history"}finally{m.value=!1}},b=r=>{if(r.title&&r.title.trim())return r.title;if(r.messages&&r.messages.length>0){const t=r.messages.find(s=>s.content&&s.content.trim());if(t)return t.content.length>50?t.content.substring(0,50)+"...":t.content}return"New Chat"},_=r=>{const t=new Date(r),a=Math.floor((new Date-t)/(1e3*60*60*24));return a===0?"Today":a===1?"Yesterday":a<7?`${a} days ago`:t.toLocaleDateString()},k=r=>r.messages?r.messages.filter(t=>t.content&&t.content.trim()).length:0;return M(()=>{x()}),(r,t)=>(o(),n(v,null,[l(d(A),{title:"Chat History - Medroid"}),l(C,{breadcrumbs:w},{default:c(()=>[e("div",L,[e("div",D,[e("div",N,[e("div",F,[e("div",S,[t[1]||(t[1]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Chat History"),e("p",{class:"text-gray-600 mt-1"},"View your previous conversations with Medroid AI")],-1)),l(d(p),{href:"/chat",class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"},{default:c(()=>t[0]||(t[0]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),e("span",null,"New Chat",-1)])),_:1})])])]),m.value?(o(),n("div",z,t[2]||(t[2]=[e("div",{class:"flex items-center justify-center py-8"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"}),e("span",{class:"ml-2 text-gray-600"},"Loading chat history...")],-1)]))):i.value?(o(),n("div",E,[e("div",I,[t[3]||(t[3]=e("svg",{class:"mx-auto h-12 w-12 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),t[4]||(t[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Error loading chat history",-1)),e("p",T,u(i.value),1),e("button",{onClick:x,class:"mt-4 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700"}," Try Again ")])])):h.value.length>0?(o(),n("div",V,[e("div",$,[e("div",P,[(o(!0),n(v,null,j(h.value,s=>(o(),B(d(p),{key:s._id||s.id,href:`/chat?conversation=${s._id||s.id}`,class:"block p-4 border border-gray-200 rounded-lg hover:border-teal-500 hover:bg-teal-50 transition-colors duration-200"},{default:c(()=>[e("div",Y,[e("div",q,[e("h3",G,u(b(s)),1),e("div",J,[e("span",null,u(k(s))+" messages",1),e("span",null,u(_(s.updated_at||s.createdAt)),1)])]),t[5]||(t[5]=e("div",{class:"flex items-center text-teal-600"},[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})])],-1))])]),_:2},1032,["href"]))),128))])])])):(o(),n("div",K,[e("div",O,[t[7]||(t[7]=e("div",{class:"w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])],-1)),t[8]||(t[8]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No chat history yet",-1)),t[9]||(t[9]=e("p",{class:"text-gray-600 mb-6"},"Start a conversation with Medroid AI to see your chat history here.",-1)),l(d(p),{href:"/chat",class:"inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"},{default:c(()=>t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),e("span",null,"Start Your First Chat",-1)])),_:1})])]))])])]),_:1})],64))}};export{et as default};
