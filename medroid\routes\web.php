<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\PaymentController;
use Inertia\Inertia;

Route::get('/', [WebController::class, 'index'])->name('home');

// Anonymous chat route - no authentication required
Route::get('anonymous-chat', [WebController::class, 'anonymousChat'])->name('anonymous-chat');

// Provider registration route - no authentication required
Route::get('providers/register', [\App\Http\Controllers\ProviderRegistrationController::class, 'showRegistrationPage'])->name('providers.register');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Dashboard data routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    Route::get('provider/dashboard-data', [\App\Http\Controllers\ProviderDashboardController::class, 'getDashboardData'])->name('provider.dashboard.data');
    Route::get('management/dashboard/kpi', [\App\Http\Controllers\AnalyticsController::class, 'getKpiMetrics'])->name('management.dashboard.kpi');

    Route::get('provider/get-availability', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getWeeklyAvailability'])->name('provider.availability.get');
    Route::get('provider/get-absences', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getAbsences'])->name('provider.absences.get');
});

// Healthcare routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])->name('providers.show');

    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('appointments');
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])->name('appointments.create');
    Route::get('appointments/{appointment}', [AppointmentController::class, 'webShow'])->name('appointments.show');
    Route::get('appointments/{appointment}/edit', [AppointmentController::class, 'webEdit'])->name('appointments.edit');
    Route::get('appointments/{appointment}/payment', [PaymentController::class, 'showPaymentPage'])->name('appointments.payment');

    // Video consultation web routes (for webapp)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])->name('video.initialize');
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])->name('video.session');
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])->name('video.join');
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])->name('video.leave');
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])->name('video.status');
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])->name('video.participant-disconnected');
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession'])->name('video.end');

    Route::get('chat', [ChatController::class, 'webIndex'])->name('chat');

    // Chat API routes for web (session-based authentication)
    Route::prefix('api/chat')->group(function () {
        Route::post('start', [\App\Http\Controllers\AIChatController::class, 'startConversation'])->name('api.chat.start');
        Route::post('message', [\App\Http\Controllers\AIChatController::class, 'sendMessage'])->name('api.chat.message');
        Route::get('history', [\App\Http\Controllers\AIChatController::class, 'getHistory'])->name('api.chat.history');
        Route::get('conversation/{conversationId}', [\App\Http\Controllers\AIChatController::class, 'getConversation'])->name('api.chat.conversation');
    });

    // Patient-specific routes
    Route::get('discover', function () {
        return Inertia::render('Discover');
    })->name('discover');

    Route::get('shop', function () {
        return Inertia::render('Shop');
    })->name('shop');

    Route::get('chat-history', function () {
        return Inertia::render('ChatHistory');
    })->name('chat-history');
});

// Create test scheduled telemedicine appointments
    Route::get('debug/create-test-appointments', function () {
        $created = [];

        // Get some patients and providers
        $patients = \App\Models\Patient::with('user')->take(2)->get();
        $providers = \App\Models\Provider::with('user')->take(2)->get();

        if ($patients->count() > 0 && $providers->count() > 0) {
            // Create appointments starting 10 minutes from now
            $startTime = now()->addMinutes(10);

            // Create 2 scheduled telemedicine appointments
            for ($i = 0; $i < 2; $i++) {
                $patient = $patients->random();
                $provider = $providers->random();

                $appointmentTime = $startTime->copy()->addMinutes($i * 30); // 30 minutes apart

                $appointment = \App\Models\Appointment::create([
                    'patient_id' => $patient->id,
                    'provider_id' => $provider->id,
                    'service_id' => null,
                    'date' => $appointmentTime->format('Y-m-d'),
                    'time_slot' => [
                        'start_time' => $appointmentTime->format('H:i'),
                        'end_time' => $appointmentTime->copy()->addHour()->format('H:i')
                    ],
                    'status' => 'scheduled',
                    'is_telemedicine' => true,
                    'amount' => 150.00,
                    'reason' => 'Video Consultation Test',
                    'notes' => 'Test appointment for video calling - Ready for testing!',
                ]);

                $created[] = "Created scheduled telemedicine appointment ID {$appointment->id} - Patient: {$patient->user->name}, Provider: {$provider->user->name}, Time: {$appointmentTime->format('Y-m-d H:i')}";
            }
        }

        return response()->json([
            'message' => 'Test appointments created for immediate testing',
            'current_time' => now()->format('Y-m-d H:i:s'),
            'first_appointment_time' => now()->addMinutes(10)->format('Y-m-d H:i:s'),
            'created' => $created
        ]);
    })->name('debug.create-test-appointments');

// Provider-specific routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    Route::get('availability', function () {
        return Inertia::render('Provider/Availability');
    })->name('provider.availability');

    Route::get('services', function () {
        return Inertia::render('Provider/Services');
    })->name('provider.services');

    Route::get('schedule', function () {
        return Inertia::render('Provider/Schedule');
    })->name('provider.schedule');

    Route::get('patients', function () {
        return Inertia::render('Provider/Patients');
    })->name('provider.patients');

    Route::get('earnings', function () {
        return Inertia::render('Provider/Earnings');
    })->name('provider.earnings');

    Route::get('profile', function () {
        return Inertia::render('Provider/Profile');
    })->name('provider.profile');
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', function () {
        return Inertia::render('Users');
    })->name('users')->middleware('permission:view users');



    // Patient management - requires 'view patients' permission
    Route::get('patients', function () {
        return Inertia::render('Patients');
    })->name('patients')->middleware('permission:view patients');

    // Appointment management - requires 'view appointments' permission
    Route::get('manage/appointments', function () {
        return Inertia::render('Appointments');
    })->name('appointments.manage')->middleware('permission:view appointments');

    // Payment management - requires 'view payments' permission
    Route::get('payments', function () {
        return Inertia::render('Payments');
    })->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', function () {
        return Inertia::render('Chats');
    })->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'manage permissions' permission
    Route::get('permissions', function () {
        return Inertia::render('Permissions');
    })->name('permissions')->middleware('permission:manage permissions');

    // Email template management - requires 'manage email templates' permission
    Route::get('email-templates', function () {
        return Inertia::render('EmailTemplates');
    })->name('email-templates')->middleware('permission:manage email templates');

    // Notification management - requires 'manage notifications' permission
    Route::get('notifications', function () {
        return Inertia::render('Notifications');
    })->name('notifications')->middleware('permission:manage notifications');

    // Service management - requires 'manage services' permission
    Route::get('services', function () {
        return Inertia::render('Services');
    })->name('services')->middleware('permission:manage services');
});

// API routes for web application (session-based authentication)
Route::middleware(['auth', 'verified'])->group(function () {
    // Services API routes (clean URLs for Vue rendering)
    Route::get('services', [ServiceController::class, 'index']);
    Route::get('services/{id}', [ServiceController::class, 'show']);
    Route::get('services/category/{category}', [ServiceController::class, 'getByCategory']);
    Route::get('service-categories', [ServiceController::class, 'getCategories']);

    // Services management routes (following availability pattern)
    Route::get('services-list', [ServiceController::class, 'index']);
    Route::post('save-service', [ServiceController::class, 'store']);
    Route::put('save-service/{id}', [ServiceController::class, 'update']);
    Route::delete('delete-service/{id}', [ServiceController::class, 'destroy']);

    // Services API routes (get- pattern for consistency)
    Route::get('get-services', [ServiceController::class, 'index']);
    Route::get('get-services/{id}', [ServiceController::class, 'show']);
    Route::get('get-services/category/{category}', [ServiceController::class, 'getByCategory']);
    Route::get('get-service-categories', [ServiceController::class, 'getCategories']);

    // Providers API routes (clean URLs for Vue rendering)
    Route::get('providers', function () {
        return Inertia::render('Providers');
    })->name('providers')->middleware('permission:view providers');
    Route::get('providers/{id}', [ProviderController::class, 'show']);

    // Providers management routes (following availability pattern)
    Route::get('providers-list', [ProviderController::class, 'index']);
    Route::get('get-providers', [ProviderController::class, 'index']);
    Route::get('get-providers/{id}', [ProviderController::class, 'show']);

    // Users management routes
    Route::get('users-list', [\App\Http\Controllers\UserController::class, 'index']);

    // Patients management routes
    Route::get('patients-list', [\App\Http\Controllers\PatientController::class, 'index']);

    // Payments management routes
    Route::get('payments-list', [\App\Http\Controllers\PaymentController::class, 'index']);

    // Chats management routes
    Route::get('chats-list', [\App\Http\Controllers\ChatController::class, 'index']);

    // Permissions management routes
    Route::get('roles-list', [\App\Http\Controllers\Auth\PermissionController::class, 'getRoles']);
    Route::get('permissions-list', [\App\Http\Controllers\Auth\PermissionController::class, 'index']);

    // Provider availability API routes
    Route::middleware('role:provider')->prefix('provider')->group(function () {
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::post('save-availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);
        Route::put('save-availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);

        // Provider absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);

        // Provider dashboard data
        Route::get('get-dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('get-appointments', [ProviderController::class, 'getAppointments']);
        Route::get('get-patients', [ProviderController::class, 'getPatients']);

        // Provider profile
        Route::get('get-profile', [ProviderController::class, 'getProfile']);
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);

        // Provider earnings
        Route::get('get-earnings', [ProviderController::class, 'getDashboardData']);
    });

    // Management dashboard API routes
    Route::middleware('permission:view dashboard')->prefix('management')->group(function () {
        Route::prefix('dashboard')->group(function () {
            Route::get('kpi', [AnalyticsController::class, 'getKpiMetrics']);
        });
    });

    // Appointment management routes (following availability and services pattern)
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments']);
    Route::get('get-appointments', [AppointmentController::class, 'userAppointments']);
    Route::post('save-appointment', [AppointmentController::class, 'store']);
    Route::put('save-appointment/{id}', [AppointmentController::class, 'update']);
    Route::delete('delete-appointment/{id}', [AppointmentController::class, 'destroy']);

    // Provider routes (following standard pattern)
    Route::get('get-providers/{id}/services', [ServiceController::class, 'index']);
    Route::get('get-providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);

    // Payment routes (following standard pattern)
    Route::post('save-appointment-with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('process-web-payment', [PaymentController::class, 'processWebPayment']);

    // Backward compatibility routes (keeping for existing integrations)
    Route::get('appointments/api', [AppointmentController::class, 'getUserAppointmentsApi']);
    Route::post('appointments/api', [AppointmentController::class, 'createAppointmentApi']);
    Route::delete('appointments/{appointmentId}/api', [AppointmentController::class, 'cancelAppointmentApi']);
    Route::post('api/appointments/with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('api/payments/process-web-payment', [PaymentController::class, 'processWebPayment']);
    Route::get('api/providers/public', [ProviderController::class, 'publicIndex']);
    Route::get('api/providers/{id}/services', [ServiceController::class, 'index']);
    Route::get('api/providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
