import{r as c,o as h,e as a,f as r,j as y,u as _,q as w,l as k,i as t,y as P,z as D,m as i,p as L,t as n,F as g,s as M,g as v,a as N}from"./vendor-bzEMSiaZ.js";import{_ as V}from"./AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const S={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},j={class:"mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},C={class:"flex-1 max-w-md"},B={class:"relative"},U=["disabled"],z={class:"bg-white rounded-lg shadow-sm border"},E={class:"p-6"},F={key:0,class:"text-center py-8"},R={key:1,class:"text-center py-8"},T={key:2,class:"space-y-4"},q={class:"flex justify-between items-start"},Q={class:"flex items-center space-x-4"},$={class:"font-medium text-gray-900"},A={class:"text-sm text-gray-600"},G={class:"flex items-center space-x-4 mt-1 text-xs text-gray-500"},H={key:0},I={key:1},J={class:"flex flex-col items-end space-y-2"},K={class:"text-sm text-gray-600"},et={__name:"Patients",setup(O){const b=[{title:"Dashboard",href:"/dashboard"},{title:"My Patients",href:"/provider/patients"}],o=c(!1),d=c([]),u=c(""),f=async()=>{o.value=!0;try{const l=await N.get("/provider/get-patients");d.value=l.data.patients||[]}catch(l){console.error("Error fetching patients:",l)}finally{o.value=!1}},m=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return h(()=>{f()}),(l,s)=>(r(),a(g,null,[y(_(w),{title:"My Patients"}),y(V,{breadcrumbs:b},{default:k(()=>[t("div",S,[s[9]||(s[9]=t("div",{class:"mb-8"},[t("h1",{class:"text-3xl font-bold text-gray-900"},"My Patients"),t("p",{class:"mt-2 text-gray-600"},"Manage your patient records and history")],-1)),t("div",j,[t("div",C,[t("div",B,[s[1]||(s[1]=t("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},null,-1)),P(t("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>u.value=e),type:"text",placeholder:"Search patients...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,u.value]])])]),t("button",{onClick:f,disabled:o.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[t("i",{class:L(["fas",o.value?"fa-spinner fa-spin":"fa-sync-alt","mr-2"])},null,2),i(" "+n(o.value?"Loading...":"Refresh"),1)],8,U)]),t("div",z,[s[8]||(s[8]=t("div",{class:"p-6 border-b"},[t("h2",{class:"text-xl font-semibold text-gray-900"},"Patient List")],-1)),t("div",E,[o.value?(r(),a("div",F,s[2]||(s[2]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"},null,-1),t("p",{class:"text-gray-600"},"Loading patients...",-1)]))):d.value.length===0?(r(),a("div",R,s[3]||(s[3]=[t("i",{class:"fas fa-users text-4xl text-gray-300 mb-4"},null,-1),t("p",{class:"text-gray-600"},"No patients found",-1),t("p",{class:"text-sm text-gray-500 mt-2"},"Patients will appear here after their first appointment",-1)]))):(r(),a("div",T,[(r(!0),a(g,null,M(d.value,e=>{var p,x;return r(),a("div",{key:e.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[t("div",q,[t("div",Q,[s[6]||(s[6]=t("div",{class:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center"},[t("i",{class:"fas fa-user text-gray-500"})],-1)),t("div",null,[t("h3",$,n(((p=e.user)==null?void 0:p.name)||"Unknown Patient"),1),t("p",A,n((x=e.user)==null?void 0:x.email),1),t("div",G,[e.date_of_birth?(r(),a("span",H,[s[4]||(s[4]=t("i",{class:"fas fa-birthday-cake mr-1"},null,-1)),i(" "+n(m(e.date_of_birth)),1)])):v("",!0),e.phone?(r(),a("span",I,[s[5]||(s[5]=t("i",{class:"fas fa-phone mr-1"},null,-1)),i(" "+n(e.phone),1)])):v("",!0)])])]),t("div",J,[t("div",K," Last visit: "+n(e.last_appointment?m(e.last_appointment):"Never"),1),s[7]||(s[7]=t("div",{class:"flex space-x-2"},[t("button",{class:"text-blue-600 hover:text-blue-800 text-sm"},[t("i",{class:"fas fa-eye mr-1"}),i(" View Records ")]),t("button",{class:"text-green-600 hover:text-green-800 text-sm"},[t("i",{class:"fas fa-calendar-plus mr-1"}),i(" Schedule ")])],-1))])])])}),128))]))])])])]),_:1})],64))}};export{et as default};
