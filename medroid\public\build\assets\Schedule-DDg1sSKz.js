import{r as p,c as y,o as A,e as l,f as o,j as D,u as j,q as N,l as U,i as e,y as L,p as d,z as E,m as c,t as n,F as v,s as T,g as b,a as I}from"./vendor-bzEMSiaZ.js";import{_ as $}from"./AppLayout.vue_vue_type_script_setup_true_lang-ikY--3ik.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const B={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},O={class:"mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"},W={class:"flex items-center space-x-4"},z={class:"flex bg-gray-100 rounded-lg p-1"},F=["disabled"],P={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},R={class:"lg:col-span-2"},q={class:"bg-white rounded-lg shadow-sm border"},Q={class:"p-6 border-b"},G={class:"text-xl font-semibold text-gray-900"},H={class:"p-6"},J={key:0,class:"text-center py-8"},K={key:1,class:"text-center py-8"},X={key:2,class:"space-y-4"},Y={class:"flex justify-between items-start"},Z={class:"flex-1"},ee={class:"flex items-center mb-2"},te={class:"font-medium text-gray-900"},se={class:"text-sm text-gray-600 space-y-1"},ae={key:0},le={key:1},oe={class:"flex flex-col items-end space-y-2"},ne={class:"flex space-x-2"},re={key:0,class:"text-green-600 hover:text-green-800 text-sm"},ie={class:"space-y-6"},de={class:"bg-white rounded-lg shadow-sm border p-6"},ce={class:"space-y-3"},ue={class:"flex justify-between"},me={class:"font-medium"},ge={class:"flex justify-between"},xe={class:"font-medium text-green-600"},pe={class:"flex justify-between"},fe={class:"font-medium text-blue-600"},ye={class:"bg-white rounded-lg shadow-sm border p-6"},ve={key:0,class:"text-gray-500 text-sm"},be={key:1,class:"space-y-3"},he={class:"font-medium text-sm"},_e={class:"text-xs text-gray-600"},we={class:"text-xs text-gray-600"},Ae={__name:"Schedule",setup(ke){const C=[{title:"Dashboard",href:"/dashboard"},{title:"My Schedule",href:"/provider/schedule"}],i=p(!1),u=p([]),x=p(new Date().toISOString().split("T")[0]),r=p("day"),h=y(()=>r.value==="day"?u.value.filter(a=>a.scheduled_at.startsWith(x.value)):u.value),f=y(()=>{const a=new Date().toISOString().split("T")[0];return u.value.filter(t=>t.scheduled_at.startsWith(a))}),_=y(()=>{const a=new Date().toISOString().split("T")[0];return u.value.filter(t=>t.scheduled_at.split("T")[0]>a).slice(0,5)}),w=async()=>{i.value=!0;try{const a=await I.get("/provider/get-appointments");u.value=a.data.appointments||[]}catch(a){console.error("Error fetching appointments:",a)}finally{i.value=!1}},k=a=>new Date(a).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),S=a=>new Date(a).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),M=a=>({scheduled:"bg-blue-100 text-blue-800",confirmed:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",V=a=>({scheduled:"fa-clock",confirmed:"fa-check-circle",in_progress:"fa-play-circle",completed:"fa-check-double",cancelled:"fa-times-circle"})[a]||"fa-clock";return A(()=>{w()}),(a,t)=>(o(),l(v,null,[D(j(N),{title:"My Schedule"}),D($,{breadcrumbs:C},{default:U(()=>[e("div",B,[t[17]||(t[17]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-gray-900"},"My Schedule"),e("p",{class:"mt-2 text-gray-600"},"View and manage your appointments")],-1)),e("div",O,[e("div",W,[e("div",z,[e("button",{onClick:t[0]||(t[0]=s=>r.value="day"),class:d(["px-3 py-1 rounded text-sm font-medium transition-colors",r.value==="day"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," Day ",2),e("button",{onClick:t[1]||(t[1]=s=>r.value="week"),class:d(["px-3 py-1 rounded text-sm font-medium transition-colors",r.value==="week"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," Week ",2),e("button",{onClick:t[2]||(t[2]=s=>r.value="month"),class:d(["px-3 py-1 rounded text-sm font-medium transition-colors",r.value==="month"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," Month ",2)]),L(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>x.value=s),type:"date",class:"border border-gray-300 rounded-lg px-3 py-2 text-sm"},null,512),[[E,x.value]])]),e("button",{onClick:w,disabled:i.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[e("i",{class:d(["fas",i.value?"fa-spinner fa-spin":"fa-sync-alt","mr-2"])},null,2),c(" "+n(i.value?"Loading...":"Refresh"),1)],8,F)]),e("div",P,[e("div",R,[e("div",q,[e("div",Q,[e("h2",G,n(r.value==="day"?S(x.value+"T00:00:00"):"Schedule"),1)]),e("div",H,[i.value?(o(),l("div",J,t[4]||(t[4]=[e("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"},null,-1),e("p",{class:"text-gray-600"},"Loading appointments...",-1)]))):h.value.length===0?(o(),l("div",K,t[5]||(t[5]=[e("i",{class:"fas fa-calendar-times text-4xl text-gray-300 mb-4"},null,-1),e("p",{class:"text-gray-600"},"No appointments scheduled for this period",-1)]))):(o(),l("div",X,[(o(!0),l(v,null,T(h.value,s=>{var m,g;return o(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Y,[e("div",Z,[e("div",ee,[e("i",{class:d(["fas",V(s.status),"text-gray-400 mr-2"])},null,2),e("h3",te,n(((g=(m=s.patient)==null?void 0:m.user)==null?void 0:g.name)||"Unknown Patient"),1)]),e("div",se,[e("p",null,[t[6]||(t[6]=e("i",{class:"fas fa-clock mr-2"},null,-1)),c(" "+n(k(s.scheduled_at)),1)]),s.service?(o(),l("p",ae,[t[7]||(t[7]=e("i",{class:"fas fa-stethoscope mr-2"},null,-1)),c(" "+n(s.service.name),1)])):b("",!0),s.notes?(o(),l("p",le,[t[8]||(t[8]=e("i",{class:"fas fa-sticky-note mr-2"},null,-1)),c(" "+n(s.notes),1)])):b("",!0)])]),e("div",oe,[e("span",{class:d(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(s.status)])},n(s.status.replace("_"," ").toUpperCase()),3),e("div",ne,[s.status==="confirmed"?(o(),l("button",re,t[9]||(t[9]=[e("i",{class:"fas fa-video mr-1"},null,-1),c(" Start ")]))):b("",!0),t[10]||(t[10]=e("button",{class:"text-blue-600 hover:text-blue-800 text-sm"},[e("i",{class:"fas fa-edit mr-1"}),c(" Edit ")],-1))])])])])}),128))]))])])]),e("div",ie,[e("div",de,[t[14]||(t[14]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Today's Summary",-1)),e("div",ce,[e("div",ue,[t[11]||(t[11]=e("span",{class:"text-gray-600"},"Total Appointments",-1)),e("span",me,n(f.value.length),1)]),e("div",ge,[t[12]||(t[12]=e("span",{class:"text-gray-600"},"Completed",-1)),e("span",xe,n(f.value.filter(s=>s.status==="completed").length),1)]),e("div",pe,[t[13]||(t[13]=e("span",{class:"text-gray-600"},"Remaining",-1)),e("span",fe,n(f.value.filter(s=>["scheduled","confirmed"].includes(s.status)).length),1)])])]),e("div",ye,[t[15]||(t[15]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Upcoming",-1)),_.value.length===0?(o(),l("div",ve," No upcoming appointments ")):(o(),l("div",be,[(o(!0),l(v,null,T(_.value,s=>{var m,g;return o(),l("div",{key:s.id,class:"border-l-4 border-blue-500 pl-3 py-2"},[e("p",he,n((g=(m=s.patient)==null?void 0:m.user)==null?void 0:g.name),1),e("p",_e,n(S(s.scheduled_at)),1),e("p",we,n(k(s.scheduled_at)),1)])}),128))]))]),t[16]||(t[16]=e("div",{class:"bg-white rounded-lg shadow-sm border p-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions"),e("div",{class:"space-y-3"},[e("button",{class:"w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("i",{class:"fas fa-calendar-alt text-blue-600 mr-3"}),e("span",{class:"text-sm font-medium"},"Manage Availability")]),e("button",{class:"w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("i",{class:"fas fa-users text-green-600 mr-3"}),e("span",{class:"text-sm font-medium"},"View Patients")]),e("button",{class:"w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("i",{class:"fas fa-chart-line text-purple-600 mr-3"}),e("span",{class:"text-sm font-medium"},"View Earnings")])])],-1))])])])]),_:1})],64))}};export{Ae as default};
