{"hash": "034e0f93", "configHash": "0328908c", "lockfileHash": "619b70f4", "browserHash": "c381a475", "optimized": {"@inertiajs/core": {"src": "../../@inertiajs/core/dist/index.esm.js", "file": "@inertiajs_core.js", "fileHash": "97a7fe30", "needsInterop": false}, "@inertiajs/vue3": {"src": "../../@inertiajs/vue3/dist/index.esm.js", "file": "@inertiajs_vue3.js", "fileHash": "71fff652", "needsInterop": false}, "@vueuse/core": {"src": "../../@vueuse/core/index.mjs", "file": "@vueuse_core.js", "fileHash": "f8f44179", "needsInterop": false}, "agora-rtc-sdk-ng": {"src": "../../agora-rtc-sdk-ng/AgoraRTC_N-production.js", "file": "agora-rtc-sdk-ng.js", "fileHash": "666067a0", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "58bf39f5", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "375bd7f1", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "4e8566f4", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "397f140b", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "7b82f1e3", "needsInterop": false}, "qrcode": {"src": "../../qrcode/lib/browser.js", "file": "qrcode.js", "fileHash": "29b030e8", "needsInterop": true}, "reka-ui": {"src": "../../reka-ui/dist/index.js", "file": "reka-ui.js", "fileHash": "8ff8389c", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "feb0f203", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "9e02ca26", "needsInterop": false}}, "chunks": {"chunk-PXYDA7QB": {"file": "chunk-PXYDA7QB.js"}, "chunk-YFHHV7KE": {"file": "chunk-YFHHV7KE.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-SLCLIWM7": {"file": "chunk-SLCLIWM7.js"}, "chunk-U3LI7FBV": {"file": "chunk-U3LI7FBV.js"}, "chunk-YHHEEY6D": {"file": "chunk-YHHEEY6D.js"}}}