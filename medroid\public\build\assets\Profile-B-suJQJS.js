import{d as w,r as O,G as ke,n as k,u as e,o as Y,v as C,f as p,l as r,T as V,g as M,af as Ce,j as a,a7 as he,a8 as $e,c as j,i as s,e as x,p as le,R as ne,m as _,J as Pe,q as Ve,x as ee,P as Be,ak as De,y as S,V as Oe,t as F,F as R,s as T,A as Me,z as te,M as q,N as se,a as oe}from"./vendor-bzEMSiaZ.js";import{_ as H}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-iCtUkjBY.js";import{_ as G}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";import{_ as z}from"./index-Oq2yf1P1.js";import{c as Ee,u as X,g as ae,e as Fe,k as Ae,_ as Ie,l as Z,d as ze,P as re,b as Le,h as ie,a as de}from"./useBodyScrollLock-D5pdoO8V.js";import{c as Se,b as A,g as Ue,_ as B,a as D}from"./Label.vue_vue_type_script_setup_true_lang-B-E--Xd1.js";import{P as K,c as U}from"./Primitive-CNCx3Yln.js";import{c as Re}from"./createLucideIcon-BJGbtoZV.js";import{_ as Te}from"./AppLayout.vue_vue_type_script_setup_true_lang-ikY--3ik.js";import{_ as je}from"./Layout.vue_vue_type_script_setup_true_lang-DO8ZLI03.js";import"./index-CLsNIGVi.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=Re("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),[I,Ne]=Ee("DialogRoot"),qe=w({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(c,{emit:d}){const o=c,n=Se(o,"open",d,{defaultValue:o.defaultOpen,passive:o.open===void 0}),m=O(),f=O(),{modal:v}=ke(o);return Ne({open:n,modal:v,openModal:()=>{n.value=!0},onOpenChange:y=>{n.value=y},onOpenToggle:()=>{n.value=!n.value},contentId:"",titleId:"",descriptionId:"",triggerElement:m,contentElement:f}),(y,g)=>k(y.$slots,"default",{open:e(n)})}}),He=w({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(c){const d=c,o=I(),{forwardRef:u,currentElement:n}=A();return o.contentId||(o.contentId=X(void 0,"reka-dialog-content")),Y(()=>{o.triggerElement.value=n.value}),(m,f)=>(p(),C(e(K),V(d,{ref:e(u),type:m.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":e(o).open.value||!1,"aria-controls":e(o).open.value?e(o).contentId:void 0,"data-state":e(o).open.value?"open":"closed",onClick:e(o).onOpenToggle}),{default:r(()=>[k(m.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),Ge=w({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(c){const d=Ue();return(o,u)=>e(d)||o.forceMount?(p(),C(Ce,{key:0,to:o.to,disabled:o.disabled,defer:o.defer},[k(o.$slots,"default")],8,["to","disabled","defer"])):M("",!0)}}),ue=w({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,n=I(),{forwardRef:m,currentElement:f}=A();return n.titleId||(n.titleId=X(void 0,"reka-dialog-title")),n.descriptionId||(n.descriptionId=X(void 0,"reka-dialog-description")),Y(()=>{n.contentElement=f,ae()!==document.body&&(n.triggerElement.value=ae())}),(v,y)=>(p(),C(e(Ie),{"as-child":"",loop:"",trapped:o.trapFocus,onMountAutoFocus:y[5]||(y[5]=g=>u("openAutoFocus",g)),onUnmountAutoFocus:y[6]||(y[6]=g=>u("closeAutoFocus",g))},{default:r(()=>[a(e(Fe),V({id:e(n).contentId,ref:e(m),as:v.as,"as-child":v.asChild,"disable-outside-pointer-events":v.disableOutsidePointerEvents,role:"dialog","aria-describedby":e(n).descriptionId,"aria-labelledby":e(n).titleId,"data-state":e(Ae)(e(n).open.value)},v.$attrs,{onDismiss:y[0]||(y[0]=g=>e(n).onOpenChange(!1)),onEscapeKeyDown:y[1]||(y[1]=g=>u("escapeKeyDown",g)),onFocusOutside:y[2]||(y[2]=g=>u("focusOutside",g)),onInteractOutside:y[3]||(y[3]=g=>u("interactOutside",g)),onPointerDownOutside:y[4]||(y[4]=g=>u("pointerDownOutside",g))}),{default:r(()=>[k(v.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),Xe=w({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,n=I(),m=Z(u),{forwardRef:f,currentElement:v}=A();return ze(v),(y,g)=>(p(),C(ue,V({...o,...e(m)},{ref:e(f),"trap-focus":e(n).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:g[0]||(g[0]=i=>{var $;i.defaultPrevented||(i.preventDefault(),($=e(n).triggerElement.value)==null||$.focus())}),onPointerDownOutside:g[1]||(g[1]=i=>{const $=i.detail.originalEvent,L=$.button===0&&$.ctrlKey===!0;($.button===2||L)&&i.preventDefault()}),onFocusOutside:g[2]||(g[2]=i=>{i.preventDefault()})}),{default:r(()=>[k(y.$slots,"default")]),_:3},16,["trap-focus"]))}}),Ye=w({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,n=Z(d);A();const m=I(),f=O(!1),v=O(!1);return(y,g)=>(p(),C(ue,V({...o,...e(n)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:g[0]||(g[0]=i=>{var $;i.defaultPrevented||(f.value||($=e(m).triggerElement.value)==null||$.focus(),i.preventDefault()),f.value=!1,v.value=!1}),onInteractOutside:g[1]||(g[1]=i=>{var E;i.defaultPrevented||(f.value=!0,i.detail.originalEvent.type==="pointerdown"&&(v.value=!0));const $=i.target;((E=e(m).triggerElement.value)==null?void 0:E.contains($))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&v.value&&i.preventDefault()})}),{default:r(()=>[k(y.$slots,"default")]),_:3},16))}}),Ze=w({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,n=I(),m=Z(u),{forwardRef:f}=A();return(v,y)=>(p(),C(e(re),{present:v.forceMount||e(n).open.value},{default:r(()=>[e(n).modal.value?(p(),C(Xe,V({key:0,ref:e(f)},{...o,...e(m),...v.$attrs}),{default:r(()=>[k(v.$slots,"default")]),_:3},16)):(p(),C(Ye,V({key:1,ref:e(f)},{...o,...e(m),...v.$attrs}),{default:r(()=>[k(v.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Je=w({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(c){const d=I();return Le(!0),A(),(o,u)=>(p(),C(e(K),{as:o.as,"as-child":o.asChild,"data-state":e(d).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:r(()=>[k(o.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),We=w({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(c){const d=I(),{forwardRef:o}=A();return(u,n)=>{var m;return(m=e(d))!=null&&m.modal.value?(p(),C(e(re),{key:0,present:u.forceMount||e(d).open.value},{default:r(()=>[a(Je,V(u.$attrs,{ref:e(o),as:u.as,"as-child":u.asChild}),{default:r(()=>[k(u.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):M("",!0)}}}),pe=w({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(c){const d=c;A();const o=I();return(u,n)=>(p(),C(e(K),V(d,{type:u.as==="button"?"button":void 0,onClick:n[0]||(n[0]=m=>e(o).onOpenChange(!1))}),{default:r(()=>[k(u.$slots,"default")]),_:3},16,["type"]))}}),Qe=w({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(c){const d=c,o=I();return A(),(u,n)=>(p(),C(e(K),V(d,{id:e(o).titleId}),{default:r(()=>[k(u.$slots,"default")]),_:3},16,["id"]))}}),et=w({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(c){const d=c;A();const o=I();return(u,n)=>(p(),C(e(K),V(d,{id:e(o).descriptionId}),{default:r(()=>[k(u.$slots,"default")]),_:3},16,["id"]))}}),tt=w({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(c){const d=c;return(o,u)=>(p(),C(e(Ge),he($e(d)),{default:r(()=>[k(o.$slots,"default")]),_:3},16))}}),st=w({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(c,{emit:d}){const n=ie(c,d);return(m,f)=>(p(),C(e(qe),V({"data-slot":"dialog"},e(n)),{default:r(()=>[k(m.$slots,"default")]),_:3},16))}}),ot=w({__name:"DialogClose",props:{asChild:{type:Boolean},as:{}},setup(c){const d=c;return(o,u)=>(p(),C(e(pe),V({"data-slot":"dialog-close"},d),{default:r(()=>[k(o.$slots,"default")]),_:3},16))}}),at=w({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=j(()=>{const{class:u,...n}=d;return n});return(u,n)=>(p(),C(e(We),V({"data-slot":"dialog-overlay"},o.value,{class:e(U)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-white/20 backdrop-blur-sm",d.class)}),{default:r(()=>[k(u.$slots,"default")]),_:3},16,["class"]))}}),lt=w({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(c,{emit:d}){const o=c,u=d,n=j(()=>{const{class:f,...v}=o;return v}),m=ie(n,u);return(f,v)=>(p(),C(e(tt),null,{default:r(()=>[a(at),a(e(Ze),V({"data-slot":"dialog-content"},e(m),{class:e(U)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",o.class)}),{default:r(()=>[k(f.$slots,"default"),a(e(pe),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:r(()=>[a(e(Ke)),v[0]||(v[0]=s("span",{class:"sr-only"},"Close",-1))]),_:1})]),_:3},16,["class"])]),_:3}))}}),nt=w({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=j(()=>{const{class:n,...m}=d;return m}),u=de(o);return(n,m)=>(p(),C(e(et),V({"data-slot":"dialog-description"},e(u),{class:e(U)("text-muted-foreground text-sm",d.class)}),{default:r(()=>[k(n.$slots,"default")]),_:3},16,["class"]))}}),rt=w({__name:"DialogFooter",props:{class:{}},setup(c){const d=c;return(o,u)=>(p(),x("div",{"data-slot":"dialog-footer",class:le(e(U)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",d.class))},[k(o.$slots,"default")],2))}}),it=w({__name:"DialogHeader",props:{class:{}},setup(c){const d=c;return(o,u)=>(p(),x("div",{"data-slot":"dialog-header",class:le(e(U)("flex flex-col gap-2 text-center sm:text-left",d.class))},[k(o.$slots,"default")],2))}}),dt=w({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(c){const d=c,o=j(()=>{const{class:n,...m}=d;return m}),u=de(o);return(n,m)=>(p(),C(e(Qe),V({"data-slot":"dialog-title"},e(u),{class:e(U)("text-lg leading-none font-semibold",d.class)}),{default:r(()=>[k(n.$slots,"default")]),_:3},16,["class"]))}}),ut=w({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{}},setup(c){const d=c;return(o,u)=>(p(),C(e(He),V({"data-slot":"dialog-trigger"},d),{default:r(()=>[k(o.$slots,"default")]),_:3},16))}}),pt={class:"space-y-6"},ct={class:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},ft={class:"grid gap-2"},mt=w({__name:"DeleteUser",setup(c){const d=O(null),o=ne({password:""}),u=m=>{m.preventDefault(),o.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>{var f;return(f=d.value)==null?void 0:f.focus()},onFinish:()=>o.reset()})},n=()=>{o.clearErrors(),o.reset()};return(m,f)=>(p(),x("div",pt,[a(H,{title:"Delete account",description:"Delete your account and all of its resources"}),s("div",ct,[f[7]||(f[7]=s("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[s("p",{class:"font-medium"},"Warning"),s("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),a(e(st),null,{default:r(()=>[a(e(ut),{"as-child":""},{default:r(()=>[a(e(z),{variant:"destructive"},{default:r(()=>f[1]||(f[1]=[_("Delete account")])),_:1})]),_:1}),a(e(lt),null,{default:r(()=>[s("form",{class:"space-y-6",onSubmit:u},[a(e(it),{class:"space-y-3"},{default:r(()=>[a(e(dt),null,{default:r(()=>f[2]||(f[2]=[_("Are you sure you want to delete your account?")])),_:1}),a(e(nt),null,{default:r(()=>f[3]||(f[3]=[_(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1})]),_:1}),s("div",ft,[a(e(B),{for:"password",class:"sr-only"},{default:r(()=>f[4]||(f[4]=[_("Password")])),_:1}),a(e(D),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:d,modelValue:e(o).password,"onUpdate:modelValue":f[0]||(f[0]=v=>e(o).password=v),placeholder:"Password"},null,8,["modelValue"]),a(G,{message:e(o).errors.password},null,8,["message"])]),a(e(rt),{class:"gap-2"},{default:r(()=>[a(e(ot),{"as-child":""},{default:r(()=>[a(e(z),{variant:"secondary",onClick:n},{default:r(()=>f[5]||(f[5]=[_(" Cancel ")])),_:1})]),_:1}),a(e(z),{variant:"destructive",disabled:e(o).processing},{default:r(()=>f[6]||(f[6]=[s("button",{type:"submit"},"Delete account",-1)])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),vt={class:"flex flex-col space-y-6"},gt={class:"grid gap-2"},_t={class:"grid gap-2"},yt={key:0},bt={class:"-mt-4 text-sm text-muted-foreground"},xt={key:0,class:"mt-2 text-sm font-medium text-green-600"},wt={class:"flex items-center gap-4"},kt={class:"text-sm text-neutral-600"},Ct={key:0,class:"flex flex-col space-y-6"},ht={key:0,class:"flex items-center justify-center py-8"},$t={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4"},Pt={class:"flex"},Vt={class:"ml-3"},Bt={class:"text-sm text-red-800"},Dt={key:2,class:"bg-green-50 border border-green-200 rounded-lg p-4"},Ot={class:"flex"},Mt={class:"ml-3"},Et={class:"text-sm text-green-800"},Ft={key:3},At={class:"bg-white rounded-lg border border-gray-200 p-6"},It={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},zt=["value"],Lt={class:"mt-4"},St={class:"mt-4"},Ut={class:"bg-white rounded-lg border border-gray-200 p-6"},Rt={class:"flex flex-wrap gap-2 mb-4"},Tt=["onClick"],jt={class:"flex gap-2"},Kt={class:"bg-white rounded-lg border border-gray-200 p-6"},Nt={class:"border border-gray-200 rounded-lg p-4 mb-4"},qt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ht={class:"md:col-span-2"},Gt={class:"flex items-center"},Xt={key:0,class:"space-y-3"},Yt={class:"flex-1"},Zt={class:"flex items-center"},Jt={class:"text-sm font-medium text-gray-900"},Wt={key:0,class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Qt={class:"text-sm text-gray-600"},es={class:"flex items-center space-x-2"},ts=["onClick"],ss=["onClick"],os={key:1,class:"text-center py-4 text-gray-500"},as={class:"bg-white rounded-lg border border-gray-200 p-6"},ls={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},ns={class:"mb-4"},rs={class:"flex items-center mb-3"},is={key:0},ds={class:"flex gap-2 mb-3"},us={key:0,class:"flex flex-wrap gap-2"},ps=["onClick"],cs={class:"bg-white rounded-lg border border-gray-200 p-6"},fs={class:"space-y-2 mb-4"},ms={class:"text-sm"},vs=["onClick"],gs={class:"flex gap-2"},_s={class:"flex justify-end pt-6 border-t"},ys={key:0,class:"flex items-center"},bs={key:1},Fs=w({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(c){const d=[{title:"Profile settings",href:"/settings/profile"}],u=Pe().props.auth.user,n=ne({name:u.name,email:u.email}),m=j(()=>u.role==="provider"),f=O(!1),v=O(!1),y=O(null),g=O(""),i=O({specialization:"",bio:"",education:"",years_of_experience:0,license_number:"",gender:"",languages:[],practice_locations:[],accepts_insurance:!1,insurance_providers:[],pricing:{consultation:0,follow_up:0},certifications:[]}),$=O(""),L=O(""),E=O(""),P=O({address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1}),ce=[{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"other",label:"Other"}],fe=()=>{n.patch(route("profile.update"),{preserveScroll:!0})},me=async()=>{var h,t;if(m.value){f.value=!0;try{const l=await oe.get("/provider/get-profile");if(l.data){const b=l.data;i.value={specialization:b.specialization||"",bio:b.bio||"",education:b.education||"",years_of_experience:b.years_of_experience||0,license_number:b.license_number||"",gender:b.gender||"",languages:b.languages||[],practice_locations:b.practice_locations||[],accepts_insurance:b.accepts_insurance||!1,insurance_providers:b.insurance_providers||[],pricing:{consultation:((h=b.pricing)==null?void 0:h.consultation)||b.consultation_fee||0,follow_up:((t=b.pricing)==null?void 0:t.follow_up)||0},certifications:b.certifications||[]}}}catch(l){console.error("Error fetching provider profile:",l),y.value="Failed to load provider profile data"}finally{f.value=!1}}},ve=async()=>{var h,t;v.value=!0,y.value=null,g.value="";try{await oe.post("/provider/profile",i.value),g.value="Provider profile updated successfully!",setTimeout(()=>{g.value=""},3e3)}catch(l){console.error("Error saving provider profile:",l),y.value=((t=(h=l.response)==null?void 0:h.data)==null?void 0:t.message)||"Failed to save provider profile"}finally{v.value=!1}},J=()=>{$.value.trim()&&!i.value.languages.includes($.value.trim())&&(i.value.languages.push($.value.trim()),$.value="")},ge=h=>{i.value.languages.splice(h,1)},W=()=>{L.value.trim()&&(i.value.certifications.push(L.value.trim()),L.value="")},_e=h=>{i.value.certifications.splice(h,1)},ye=()=>{P.value.address.trim()&&(i.value.practice_locations.push({address:P.value.address.trim(),city:P.value.city.trim(),state:P.value.state.trim(),zip_code:P.value.zip_code.trim(),coordinates:P.value.coordinates,is_primary:P.value.is_primary||i.value.practice_locations.length===0}),P.value={address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1})},be=h=>{i.value.practice_locations.splice(h,1)},xe=h=>{i.value.practice_locations.forEach((t,l)=>{t.is_primary=l===h})},Q=()=>{E.value.trim()&&!i.value.insurance_providers.includes(E.value.trim())&&(i.value.insurance_providers.push(E.value.trim()),E.value="")},we=h=>{i.value.insurance_providers.splice(h,1)};return Y(()=>{m.value&&me()}),(h,t)=>(p(),C(Te,{breadcrumbs:d},{default:r(()=>[a(e(Ve),{title:"Profile settings"}),a(je,null,{default:r(()=>[s("div",vt,[a(H,{title:"Profile information",description:"Update your name and email address"}),s("form",{onSubmit:ee(fe,["prevent"]),class:"space-y-6"},[s("div",gt,[a(e(B),{for:"name"},{default:r(()=>t[19]||(t[19]=[_("Name")])),_:1}),a(e(D),{id:"name",class:"mt-1 block w-full",modelValue:e(n).name,"onUpdate:modelValue":t[0]||(t[0]=l=>e(n).name=l),required:"",autocomplete:"name",placeholder:"Full name"},null,8,["modelValue"]),a(G,{class:"mt-2",message:e(n).errors.name},null,8,["message"])]),s("div",_t,[a(e(B),{for:"email"},{default:r(()=>t[20]||(t[20]=[_("Email address")])),_:1}),a(e(D),{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e(n).email,"onUpdate:modelValue":t[1]||(t[1]=l=>e(n).email=l),required:"",autocomplete:"username",placeholder:"Email address"},null,8,["modelValue"]),a(G,{class:"mt-2",message:e(n).errors.email},null,8,["message"])]),h.mustVerifyEmail&&!e(u).email_verified_at?(p(),x("div",yt,[s("p",bt,[t[22]||(t[22]=_(" Your email address is unverified. ")),a(e(Be),{href:h.route("verification.send"),method:"post",as:"button",class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:r(()=>t[21]||(t[21]=[_(" Click here to resend the verification email. ")])),_:1},8,["href"])]),h.status==="verification-link-sent"?(p(),x("div",xt," A new verification link has been sent to your email address. ")):M("",!0)])):M("",!0),s("div",wt,[a(e(z),{disabled:e(n).processing},{default:r(()=>t[23]||(t[23]=[_("Save")])),_:1},8,["disabled"]),a(De,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:r(()=>[S(s("p",kt,"Saved.",512),[[Oe,e(n).recentlySuccessful]])]),_:1})])],32)]),m.value?(p(),x("div",Ct,[f.value?(p(),x("div",ht,t[24]||(t[24]=[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),s("span",{class:"ml-2 text-gray-600"},"Loading provider settings...",-1)]))):M("",!0),y.value?(p(),x("div",$t,[s("div",Pt,[t[25]||(t[25]=s("div",{class:"flex-shrink-0"},[s("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[s("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),s("div",Vt,[s("p",Bt,F(y.value),1)])])])):M("",!0),g.value?(p(),x("div",Dt,[s("div",Ot,[t[26]||(t[26]=s("div",{class:"flex-shrink-0"},[s("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[s("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),s("div",Mt,[s("p",Et,F(g.value),1)])])])):M("",!0),f.value?M("",!0):(p(),x("div",Ft,[a(H,{title:"Provider Settings",description:"Manage your professional profile and appointment preferences"}),s("form",{onSubmit:ee(ve,["prevent"]),class:"space-y-8 mt-6"},[s("div",At,[t[34]||(t[34]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Professional Information",-1)),s("div",It,[s("div",null,[a(e(B),{for:"specialization"},{default:r(()=>t[27]||(t[27]=[_("Specialization *")])),_:1}),a(e(D),{id:"specialization",modelValue:i.value.specialization,"onUpdate:modelValue":t[2]||(t[2]=l=>i.value.specialization=l),placeholder:"e.g., Cardiology, Dermatology",required:"",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"license_number"},{default:r(()=>t[28]||(t[28]=[_("License Number")])),_:1}),a(e(D),{id:"license_number",modelValue:i.value.license_number,"onUpdate:modelValue":t[3]||(t[3]=l=>i.value.license_number=l),placeholder:"Medical license number",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"years_of_experience"},{default:r(()=>t[29]||(t[29]=[_("Years of Experience")])),_:1}),a(e(D),{id:"years_of_experience",modelValue:i.value.years_of_experience,"onUpdate:modelValue":t[4]||(t[4]=l=>i.value.years_of_experience=l),modelModifiers:{number:!0},type:"number",min:"0",placeholder:"0",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"gender"},{default:r(()=>t[30]||(t[30]=[_("Gender")])),_:1}),S(s("select",{id:"gender","onUpdate:modelValue":t[5]||(t[5]=l=>i.value.gender=l),class:"mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},[t[31]||(t[31]=s("option",{value:""},"Select Gender",-1)),(p(),x(R,null,T(ce,l=>s("option",{key:l.value,value:l.value},F(l.label),9,zt)),64))],512),[[Me,i.value.gender]])])]),s("div",Lt,[a(e(B),{for:"education"},{default:r(()=>t[32]||(t[32]=[_("Education")])),_:1}),S(s("textarea",{id:"education","onUpdate:modelValue":t[6]||(t[6]=l=>i.value.education=l),rows:"3",placeholder:"Medical school, residency, fellowships...",class:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},null,512),[[te,i.value.education]])]),s("div",St,[a(e(B),{for:"bio"},{default:r(()=>t[33]||(t[33]=[_("Bio")])),_:1}),S(s("textarea",{id:"bio","onUpdate:modelValue":t[7]||(t[7]=l=>i.value.bio=l),rows:"4",placeholder:"Tell patients about yourself, your approach to care, and your experience...",class:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"},null,512),[[te,i.value.bio]])])]),s("div",Ut,[t[37]||(t[37]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Languages",-1)),s("div",Rt,[(p(!0),x(R,null,T(i.value.languages,(l,b)=>(p(),x("span",{key:b,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[_(F(l)+" ",1),s("button",{onClick:N=>ge(b),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},t[35]||(t[35]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Tt)]))),128))]),s("div",jt,[a(e(D),{modelValue:$.value,"onUpdate:modelValue":t[8]||(t[8]=l=>$.value=l),onKeyup:q(J,["enter"]),placeholder:"Add a language",class:"flex-1"},null,8,["modelValue"]),a(e(z),{onClick:J,type:"button",variant:"outline"},{default:r(()=>t[36]||(t[36]=[_(" Add ")])),_:1})])]),s("div",Kt,[t[46]||(t[46]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Practice Locations",-1)),s("div",Nt,[t[44]||(t[44]=s("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Add New Location",-1)),s("div",qt,[s("div",Ht,[a(e(B),{for:"new_address"},{default:r(()=>t[38]||(t[38]=[_("Address")])),_:1}),a(e(D),{id:"new_address",modelValue:P.value.address,"onUpdate:modelValue":t[9]||(t[9]=l=>P.value.address=l),placeholder:"Street address",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"new_city"},{default:r(()=>t[39]||(t[39]=[_("City")])),_:1}),a(e(D),{id:"new_city",modelValue:P.value.city,"onUpdate:modelValue":t[10]||(t[10]=l=>P.value.city=l),placeholder:"City",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"new_state"},{default:r(()=>t[40]||(t[40]=[_("State")])),_:1}),a(e(D),{id:"new_state",modelValue:P.value.state,"onUpdate:modelValue":t[11]||(t[11]=l=>P.value.state=l),placeholder:"State",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"new_zip"},{default:r(()=>t[41]||(t[41]=[_("ZIP Code")])),_:1}),a(e(D),{id:"new_zip",modelValue:P.value.zip_code,"onUpdate:modelValue":t[12]||(t[12]=l=>P.value.zip_code=l),placeholder:"ZIP Code",class:"mt-1"},null,8,["modelValue"])]),s("div",Gt,[S(s("input",{"onUpdate:modelValue":t[13]||(t[13]=l=>P.value.is_primary=l),type:"checkbox",id:"is_primary_new",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[se,P.value.is_primary]]),t[42]||(t[42]=s("label",{for:"is_primary_new",class:"ml-2 block text-sm text-gray-700"}," Set as primary location ",-1))])]),a(e(z),{onClick:ye,type:"button",variant:"outline",class:"mt-3"},{default:r(()=>t[43]||(t[43]=[s("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),_(" Add Location ")])),_:1})]),i.value.practice_locations.length>0?(p(),x("div",Xt,[(p(!0),x(R,null,T(i.value.practice_locations,(l,b)=>(p(),x("div",{key:b,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[s("div",Yt,[s("div",Zt,[s("span",Jt,F(l.address),1),l.is_primary?(p(),x("span",Wt," Primary ")):M("",!0)]),s("p",Qt,F(l.city)+", "+F(l.state)+" "+F(l.zip_code),1)]),s("div",es,[l.is_primary?M("",!0):(p(),x("button",{key:0,onClick:N=>xe(b),type:"button",class:"text-sm text-blue-600 hover:text-blue-800"}," Set Primary ",8,ts)),s("button",{onClick:N=>be(b),type:"button",class:"text-red-600 hover:text-red-800"},t[45]||(t[45]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,ss)])]))),128))])):(p(),x("div",os," No practice locations added yet "))]),s("div",as,[t[53]||(t[53]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Pricing & Insurance",-1)),s("div",ls,[s("div",null,[a(e(B),{for:"consultation_fee"},{default:r(()=>t[47]||(t[47]=[_("Consultation Fee ($)")])),_:1}),a(e(D),{id:"consultation_fee",modelValue:i.value.pricing.consultation,"onUpdate:modelValue":t[14]||(t[14]=l=>i.value.pricing.consultation=l),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])]),s("div",null,[a(e(B),{for:"follow_up_fee"},{default:r(()=>t[48]||(t[48]=[_("Follow-up Fee ($)")])),_:1}),a(e(D),{id:"follow_up_fee",modelValue:i.value.pricing.follow_up,"onUpdate:modelValue":t[15]||(t[15]=l=>i.value.pricing.follow_up=l),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])])]),s("div",ns,[s("div",rs,[S(s("input",{"onUpdate:modelValue":t[16]||(t[16]=l=>i.value.accepts_insurance=l),type:"checkbox",id:"accepts_insurance",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[se,i.value.accepts_insurance]]),t[49]||(t[49]=s("label",{for:"accepts_insurance",class:"ml-2 block text-sm font-medium text-gray-700"}," Accept Insurance ",-1))])]),i.value.accepts_insurance?(p(),x("div",is,[a(e(B),{class:"block text-sm font-medium text-gray-700 mb-2"},{default:r(()=>t[50]||(t[50]=[_("Insurance Providers")])),_:1}),s("div",ds,[a(e(D),{modelValue:E.value,"onUpdate:modelValue":t[17]||(t[17]=l=>E.value=l),placeholder:"Add insurance provider",class:"flex-1",onKeyup:q(Q,["enter"])},null,8,["modelValue"]),a(e(z),{onClick:Q,type:"button",variant:"outline"},{default:r(()=>t[51]||(t[51]=[_(" Add ")])),_:1})]),i.value.insurance_providers.length>0?(p(),x("div",us,[(p(!0),x(R,null,T(i.value.insurance_providers,(l,b)=>(p(),x("span",{key:b,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[_(F(l)+" ",1),s("button",{onClick:N=>we(b),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},t[52]||(t[52]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ps)]))),128))])):M("",!0)])):M("",!0)]),s("div",cs,[t[56]||(t[56]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Certifications",-1)),s("div",fs,[(p(!0),x(R,null,T(i.value.certifications,(l,b)=>(p(),x("div",{key:b,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[s("span",ms,F(l),1),s("button",{onClick:N=>_e(b),type:"button",class:"text-red-600 hover:text-red-800"},t[54]||(t[54]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,vs)]))),128))]),s("div",gs,[a(e(D),{modelValue:L.value,"onUpdate:modelValue":t[18]||(t[18]=l=>L.value=l),onKeyup:q(W,["enter"]),placeholder:"Add a certification",class:"flex-1"},null,8,["modelValue"]),a(e(z),{onClick:W,type:"button",variant:"outline"},{default:r(()=>t[55]||(t[55]=[_(" Add ")])),_:1})])]),s("div",_s,[a(e(z),{type:"submit",disabled:v.value,class:"min-w-[120px]"},{default:r(()=>[v.value?(p(),x("div",ys,t[57]||(t[57]=[s("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),_(" Saving... ")]))):(p(),x("span",bs,"Save Provider Settings"))]),_:1},8,["disabled"])])],32)]))])):M("",!0),a(mt)]),_:1})]),_:1}))}});export{Fs as default};
