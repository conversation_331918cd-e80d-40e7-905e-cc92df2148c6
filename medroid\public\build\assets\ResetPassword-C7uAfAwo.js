import{d as _,R as g,v as f,f as u,l as t,j as a,i as l,u as s,q as V,x as b,m as i,g as k}from"./vendor-bzEMSiaZ.js";import{_ as d}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";import{_ as y}from"./index-Oq2yf1P1.js";import{_ as m,a as n}from"./Label.vue_vue_type_script_setup_true_lang-B-E--Xd1.js";import{L as C,_ as v}from"./AuthLayout.vue_vue_type_script_setup_true_lang-CXnZV5tQ.js";import"./Primitive-CNCx3Yln.js";import"./index-CLsNIGVi.js";import"./createLucideIcon-BJGbtoZV.js";const x={class:"grid gap-6"},P={class:"grid gap-2"},R={class:"grid gap-2"},$={class:"grid gap-2"},S=_({__name:"ResetPassword",props:{token:{},email:{}},setup(w){const p=w,e=g({token:p.token,email:p.email,password:"",password_confirmation:""}),c=()=>{e.post(route("password.store"),{onFinish:()=>{e.reset("password","password_confirmation")}})};return(N,o)=>(u(),f(v,{title:"Reset password",description:"Please enter your new password below"},{default:t(()=>[a(s(V),{title:"Reset password"}),l("form",{onSubmit:b(c,["prevent"])},[l("div",x,[l("div",P,[a(s(m),{for:"email"},{default:t(()=>o[3]||(o[3]=[i("Email")])),_:1}),a(s(n),{id:"email",type:"email",name:"email",autocomplete:"email",modelValue:s(e).email,"onUpdate:modelValue":o[0]||(o[0]=r=>s(e).email=r),class:"mt-1 block w-full",readonly:""},null,8,["modelValue"]),a(d,{message:s(e).errors.email,class:"mt-2"},null,8,["message"])]),l("div",R,[a(s(m),{for:"password"},{default:t(()=>o[4]||(o[4]=[i("Password")])),_:1}),a(s(n),{id:"password",type:"password",name:"password",autocomplete:"new-password",modelValue:s(e).password,"onUpdate:modelValue":o[1]||(o[1]=r=>s(e).password=r),class:"mt-1 block w-full",autofocus:"",placeholder:"Password"},null,8,["modelValue"]),a(d,{message:s(e).errors.password},null,8,["message"])]),l("div",$,[a(s(m),{for:"password_confirmation"},{default:t(()=>o[5]||(o[5]=[i(" Confirm Password ")])),_:1}),a(s(n),{id:"password_confirmation",type:"password",name:"password_confirmation",autocomplete:"new-password",modelValue:s(e).password_confirmation,"onUpdate:modelValue":o[2]||(o[2]=r=>s(e).password_confirmation=r),class:"mt-1 block w-full",placeholder:"Confirm password"},null,8,["modelValue"]),a(d,{message:s(e).errors.password_confirmation},null,8,["message"])]),a(s(y),{type:"submit",class:"mt-4 w-full",disabled:s(e).processing},{default:t(()=>[s(e).processing?(u(),f(s(C),{key:0,class:"h-4 w-4 animate-spin"})):k("",!0),o[6]||(o[6]=i(" Reset password "))]),_:1},8,["disabled"])])],32)]),_:1}))}});export{S as default};
