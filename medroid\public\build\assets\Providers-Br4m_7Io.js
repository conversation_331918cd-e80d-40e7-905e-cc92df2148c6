import{_}from"./AppLayout.vue_vue_type_script_setup_true_lang-ikY--3ik.js";import{r as y,o as k,e as l,f as i,j as u,u as m,q as b,l as g,i as t,t as r,F as c,s as f,p as w,g as p,v as A,P,m as B}from"./vendor-bzEMSiaZ.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const V={class:"flex items-center justify-between"},C={class:"flex mt-2","aria-label":"Breadcrumb"},j={class:"inline-flex items-center space-x-1 md:space-x-3"},z={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},M={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},N={class:"py-12"},S={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},F={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},D={class:"p-6"},L={class:"flex items-center"},R={class:"ml-4"},T={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},$={class:"p-6"},G={class:"flex items-center"},H={class:"ml-4"},I={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},J={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},O={class:"flex items-center"},Q={class:"ml-4"},U={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},W={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6 text-gray-900 dark:text-gray-100"},at={key:0,class:"text-center py-8"},rt={key:1,class:"overflow-x-auto"},it={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},lt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"px-6 py-4 whitespace-nowrap"},ot={class:"flex items-center"},nt={class:"ml-4"},xt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},gt={class:"text-sm text-gray-500 dark:text-gray-400"},ct={class:"px-6 py-4 whitespace-nowrap"},yt={class:"text-sm text-gray-900 dark:text-gray-100"},ut={class:"text-sm text-gray-500 dark:text-gray-400"},mt={class:"px-6 py-4 whitespace-nowrap"},ft={class:"px-6 py-4 whitespace-nowrap"},pt={class:"flex items-center"},vt={class:"text-sm text-gray-900 dark:text-gray-100"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},_t={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},kt={key:0,class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"},jt={__name:"Providers",setup(bt){const n=[{title:"Dashboard",href:"/dashboard"},{title:"Providers",href:"/providers"}],x=y(!1),a=y([]),v=async()=>{x.value=!0;try{const d=await window.axios.get("/providers-list");a.value=d.data.data||d.data||[]}catch(d){console.error("Error fetching providers:",d),a.value=[]}finally{x.value=!1}},h=d=>({verified:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"})[d]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";return k(()=>{v()}),(d,e)=>(i(),l(c,null,[u(m(b),{title:"Provider Management"}),u(_,null,{header:g(()=>[t("div",V,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Provider Management ",-1)),t("nav",C,[t("ol",j,[(i(),l(c,null,f(n,(s,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<n.length-1?(i(),A(m(P),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:g(()=>[B(r(s.title),1)]),_:2},1032,["href"])):(i(),l("span",z,r(s.title),1)),o<n.length-1?(i(),l("svg",M,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):p("",!0)])),64))])])]),e[2]||(e[2]=t("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Provider ",-1))])]),default:g(()=>[t("div",N,[t("div",S,[t("div",E,[t("div",F,[t("div",D,[t("div",L,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-md text-2xl text-blue-500"})],-1)),t("div",R,[e[3]||(e[3]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Providers",-1)),t("p",T,r(Array.isArray(a.value)?a.value.length:0),1)])])])]),t("div",q,[t("div",$,[t("div",G,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-check-circle text-2xl text-green-500"})],-1)),t("div",H,[e[5]||(e[5]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Verified",-1)),t("p",I,r(Array.isArray(a.value)?a.value.filter(s=>s.verification_status==="verified").length:0),1)])])])]),t("div",J,[t("div",K,[t("div",O,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-clock text-2xl text-yellow-500"})],-1)),t("div",Q,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Pending",-1)),t("p",U,r(Array.isArray(a.value)?a.value.filter(s=>s.verification_status==="pending").length:0),1)])])])]),t("div",W,[t("div",X,[t("div",Y,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-star text-2xl text-yellow-400"})],-1)),t("div",Z,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Avg Rating",-1)),t("p",tt,r(Array.isArray(a.value)&&a.value.length>0?(a.value.reduce((s,o)=>s+(o.rating||0),0)/a.value.length).toFixed(1):"0.0"),1)])])])])]),t("div",et,[t("div",st,[x.value?(i(),l("div",at,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(i(),l("div",rt,[t("table",it,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Provider "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Specialization "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Rating "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patients "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",lt,[(i(!0),l(c,null,f(Array.isArray(a.value)?a.value:[],s=>(i(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",dt,[t("div",ot,[e[12]||(e[12]=t("div",{class:"flex-shrink-0 h-10 w-10"},[t("div",{class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center"},[t("i",{class:"fas fa-user-md text-blue-600 dark:text-blue-400"})])],-1)),t("div",nt,[t("div",xt,r(s.name),1),t("div",gt,r(s.email),1)])])]),t("td",ct,[t("div",yt,r(s.specialization),1),t("div",ut,r(s.license_number),1)]),t("td",mt,[t("span",{class:w([h(s.verification_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(s.verification_status),3)]),t("td",ft,[t("div",pt,[e[13]||(e[13]=t("i",{class:"fas fa-star text-yellow-400 mr-1"},null,-1)),t("span",vt,r(s.rating),1)])]),t("td",ht,r(s.patients_count),1),t("td",_t,[e[14]||(e[14]=t("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," View ",-1)),e[15]||(e[15]=t("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"}," Edit ",-1)),s.verification_status==="pending"?(i(),l("button",kt," Verify ")):p("",!0)])]))),128))])])]))])])])])]),_:1})],64))}};export{jt as default};
