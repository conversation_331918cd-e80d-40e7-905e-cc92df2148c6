import{r as j,c as d,o as te,H as re,e as i,f as r,i as t,j as h,g as _,F as V,s as H,l as p,t as x,p as y,u as l,P as v,m as M,v as b,a as ae,d as w,n as k,J as oe}from"./vendor-bzEMSiaZ.js";import{M as ne}from"./MedroidLogo-c6q-kzU_.js";import{_ as ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as B,P as le}from"./Primitive-CNCx3Yln.js";import{c as ue}from"./createLucideIcon-BJGbtoZV.js";const de={class:"h-full bg-white border-r border-gray-200 text-gray-800 flex flex-col"},ce={class:"flex items-center justify-between p-3 border-b border-gray-200"},pe={class:"flex items-center"},me={key:0,class:"ml-2 text-lg font-semibold text-medroid-navy"},he={class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fe={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},ve={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"},_e={class:"flex-1 overflow-y-auto py-3"},ge={class:"space-y-1 px-2 flex flex-col h-full"},xe={class:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ye=["d"],be={key:0,class:"ml-2"},we={key:0,class:"pt-6 flex flex-col flex-1 min-h-0"},ke={class:"px-3 mb-2"},Me={class:"flex items-center justify-between"},$e={class:"flex-1 min-h-0 overflow-y-auto"},ze={key:0,class:"px-3 py-2"},Ce={key:1,class:"space-y-1 px-3"},Ve={class:"flex-1 min-w-0"},Be={class:"text-gray-900 font-medium truncate group-hover:text-medroid-orange"},Ae={class:"text-gray-500 text-xs mt-0.5"},He={key:2,class:"px-3 py-2"},je={key:0,class:"px-3 py-3 border-t border-gray-200 mt-auto"},Se={key:1,class:"pt-6"},Le={key:0,class:"px-3 mb-2"},Pe={class:"w-5 h-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie=["d"],Ne={key:0,class:"ml-3"},De={class:"border-t border-gray-200 p-3"},Ee={class:"flex items-center"},Re={key:0,class:"ml-2 flex-1"},We={class:"text-sm font-medium text-gray-900"},Te={class:"text-xs text-gray-500 capitalize"},Ue={key:0,class:"mt-3 space-y-1"},qe={key:1,class:"mt-3"},Fe={__name:"AppSidebar",props:{user:{type:Object,required:!0}},setup(u){const a=u,o=j(!1),c=j([]),g=j(!1),C=d(()=>{var s;return((s=a.user)==null?void 0:s.role)||"patient"}),m=d(()=>C.value==="admin"),S=d(()=>C.value==="provider"),A=d(()=>C.value==="patient"),R=d(()=>{const s=[];return(m.value||S.value)&&s.push({title:"Dashboard",href:"/dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",permission:!0}),A.value&&s.push({title:"Chat",href:"/chat",icon:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",permission:!0},{title:"Discover",href:"/discover",icon:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",permission:!0},{title:"Shop",href:"/shop",icon:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z",permission:!0},{title:"Appointments",href:"/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0}),S.value&&s.push({title:"Schedule",href:"/provider/schedule",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0},{title:"Availability",href:"/provider/availability",icon:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0},{title:"My Patients",href:"/provider/patients",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:!0},{title:"Services",href:"/provider/services",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",permission:!0},{title:"Earnings",href:"/provider/earnings",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0}),s}),L=d(()=>{var s,e,f,n,$,z,I,N,D,E;return m.value?[{title:"Users",href:"/users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:(e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view users")},{title:"Providers",href:"/providers",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",permission:(n=(f=a.user)==null?void 0:f.user_permissions)==null?void 0:n.includes("view providers")},{title:"Patients",href:"/patients",icon:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",permission:(z=($=a.user)==null?void 0:$.user_permissions)==null?void 0:z.includes("view patients")},{title:"Appointments",href:"/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:(N=(I=a.user)==null?void 0:I.user_permissions)==null?void 0:N.includes("view appointments")},{title:"Payments",href:"/payments",icon:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z",permission:(E=(D=a.user)==null?void 0:D.user_permissions)==null?void 0:E.includes("view payments")}].filter(se=>se.permission):[]}),W=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view users"))}),T=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view providers"))}),U=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view patients"))});d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view analytics"))});const q=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view appointments"))}),F=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view payments"))}),J=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("view chats"))}),O=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage notifications"))}),G=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage email templates"))}),K=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage permissions"))}),Q=d(()=>{var s,e;return m.value||((e=(s=a.user)==null?void 0:s.user_permissions)==null?void 0:e.includes("manage services"))});d(()=>W.value||T.value||U.value||q.value||F.value||J.value||Q.value),d(()=>K.value||G.value||O.value);const X=()=>{o.value=!o.value,localStorage.setItem("app-sidebar-collapsed",o.value)},P=()=>{window.innerWidth<768&&!o.value&&(o.value=!0,localStorage.setItem("app-sidebar-collapsed","true"))},Y=async()=>{if(A.value){g.value=!0;try{const e=(await ae.get("/api/chat/history")).data;let f=[];Array.isArray(e)?f=e:e.conversations&&Array.isArray(e.conversations)?f=e.conversations:e.data&&Array.isArray(e.data)&&(f=e.data);const n=f.filter($=>!$.messages||!Array.isArray($.messages)?!1:$.messages.some(z=>z&&z.content&&z.content.trim().length>0));c.value=n.slice(0,8)}catch(s){console.error("Error fetching chat history:",s),c.value=[]}finally{g.value=!1}}},Z=s=>{if(s.title&&s.title.trim())return s.title.length>25?s.title.substring(0,25)+"...":s.title;if(s.messages&&s.messages.length>0){const e=s.messages[0].content;return e.length>25?e.substring(0,25)+"...":e}return"New Chat"},ee=s=>{const e=new Date(s),n=Math.floor((new Date-e)/(1e3*60*60));return n<1?"Just now":n<24?`${n}h ago`:n<168?`${Math.floor(n/24)}d ago`:e.toLocaleDateString()};return te(()=>{const s=localStorage.getItem("app-sidebar-collapsed");s!==null&&(o.value=s==="true"),window.innerWidth<768&&(o.value=!0),window.addEventListener("resize",P),A.value&&Y()}),re(()=>{window.removeEventListener("resize",P)}),(s,e)=>{var f;return r(),i("div",{class:y(["h-screen flex-shrink-0",[o.value?"w-16":"w-64","transition-all duration-300"]])},[t("div",de,[t("div",ce,[t("div",pe,[h(ne,{size:28}),o.value?_("",!0):(r(),i("span",me,"Medroid"))]),t("button",{onClick:X,class:"text-gray-400 hover:text-gray-600 focus:outline-none"},[(r(),i("svg",he,[o.value?(r(),i("path",fe)):(r(),i("path",ve))]))])]),t("div",_e,[t("nav",ge,[(r(!0),i(V,null,H(R.value,n=>(r(),i("div",{key:n.href},[h(l(v),{href:n.href,class:y(["flex items-center px-2 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200",[s.$page.url===n.href||s.$page.url.startsWith(n.href+"/")?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:p(()=>[(r(),i("svg",xe,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.icon},null,8,ye)])),o.value?_("",!0):(r(),i("span",be,x(n.title),1))]),_:2},1032,["href","class"])]))),128)),A.value&&!o.value?(r(),i("div",we,[t("div",ke,[t("div",Me,[e[1]||(e[1]=t("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Recent Chats",-1)),h(l(v),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark",title:"Start new chat"},{default:p(()=>e[0]||(e[0]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1})])]),t("div",$e,[g.value?(r(),i("div",ze,e[2]||(e[2]=[t("div",{class:"flex items-center text-xs text-gray-500"},[t("div",{class:"animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400 mr-2"}),M(" Loading chats... ")],-1)]))):c.value.length>0?(r(),i("div",Ce,[(r(!0),i(V,null,H(c.value,n=>(r(),b(l(v),{key:n._id||n.id,href:`/chat?conversation=${n._id||n.id}`,class:"flex items-start py-2 text-xs rounded-lg transition-colors duration-200 hover:bg-gray-50 group block"},{default:p(()=>[e[3]||(e[3]=t("div",{class:"flex-shrink-0 mr-2 mt-0.5"},[t("div",{class:"w-2 h-2 bg-medroid-orange rounded-full"})],-1)),t("div",Ve,[t("p",Be,x(Z(n)),1),t("p",Ae,x(ee(n.updated_at||n.createdAt)),1)])]),_:2},1032,["href"]))),128))])):(r(),i("div",He,[e[5]||(e[5]=t("p",{class:"text-xs text-gray-500"},"No recent chats",-1)),h(l(v),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark mt-1 inline-block"},{default:p(()=>e[4]||(e[4]=[M(" Start your first chat ")])),_:1})]))]),c.value.length>0?(r(),i("div",je,[h(l(v),{href:"/chat-history",class:"text-xs text-gray-600 hover:text-medroid-orange flex items-center justify-center w-full py-2 rounded-lg hover:bg-gray-50 transition-colors"},{default:p(()=>e[6]||(e[6]=[t("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1),M(" View all chats ")])),_:1})])):_("",!0)])):_("",!0),L.value.length>0?(r(),i("div",Se,[o.value?_("",!0):(r(),i("div",Le,e[7]||(e[7]=[t("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Management",-1)]))),(r(!0),i(V,null,H(L.value,n=>(r(),i("div",{key:n.href},[h(l(v),{href:n.href,class:y(["flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200",[s.$page.url===n.href||s.$page.url.startsWith(n.href+"/")?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:p(()=>[(r(),i("svg",Pe,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.icon},null,8,Ie)])),o.value?_("",!0):(r(),i("span",Ne,x(n.title),1))]),_:2},1032,["href","class"])]))),128))])):_("",!0)])]),t("div",De,[t("div",Ee,[e[8]||(e[8]=t("div",{class:"w-7 h-7 bg-gray-300 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),o.value?_("",!0):(r(),i("div",Re,[t("p",We,x((f=u.user)==null?void 0:f.name),1),t("p",Te,x(C.value),1)]))]),o.value?(r(),i("div",qe,[h(l(v),{href:"/logout",method:"post",class:"flex items-center justify-center p-2 text-red-600 hover:text-red-800 rounded",title:"Logout"},{default:p(()=>e[11]||(e[11]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1)])),_:1})])):(r(),i("div",Ue,[h(l(v),{href:"/settings/profile",class:"flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-900 rounded"},{default:p(()=>e[9]||(e[9]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),M(" Settings ")])),_:1}),h(l(v),{href:"/logout",method:"post",class:"flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 rounded"},{default:p(()=>e[10]||(e[10]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),M(" Logout ")])),_:1})]))])])],2)}}},Je=ie(Fe,[["__scopeId","data-v-2dfe6401"]]),Oe=w({__name:"Breadcrumb",props:{class:{}},setup(u){const a=u;return(o,c)=>(r(),i("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:y(a.class)},[k(o.$slots,"default")],2))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=ue("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),Ke=w({__name:"BreadcrumbItem",props:{class:{}},setup(u){const a=u;return(o,c)=>(r(),i("li",{"data-slot":"breadcrumb-item",class:y(l(B)("inline-flex items-center gap-1.5",a.class))},[k(o.$slots,"default")],2))}}),Qe=w({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(u){const a=u;return(o,c)=>(r(),b(l(le),{"data-slot":"breadcrumb-link",as:o.as,"as-child":o.asChild,class:y(l(B)("hover:text-foreground transition-colors",a.class))},{default:p(()=>[k(o.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Xe=w({__name:"BreadcrumbList",props:{class:{}},setup(u){const a=u;return(o,c)=>(r(),i("ol",{"data-slot":"breadcrumb-list",class:y(l(B)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",a.class))},[k(o.$slots,"default")],2))}}),Ye=w({__name:"BreadcrumbPage",props:{class:{}},setup(u){const a=u;return(o,c)=>(r(),i("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:y(l(B)("text-foreground font-normal",a.class))},[k(o.$slots,"default")],2))}}),Ze=w({__name:"BreadcrumbSeparator",props:{class:{}},setup(u){const a=u;return(o,c)=>(r(),i("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:y(l(B)("[&>svg]:size-3.5",a.class))},[k(o.$slots,"default",{},()=>[h(l(Ge))])],2))}}),es=w({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(u){return(a,o)=>(r(),b(l(Oe),null,{default:p(()=>[h(l(Xe),null,{default:p(()=>[(r(!0),i(V,null,H(a.breadcrumbs,(c,g)=>(r(),i(V,{key:g},[h(l(Ke),null,{default:p(()=>[g===a.breadcrumbs.length-1?(r(),b(l(Ye),{key:0},{default:p(()=>[M(x(c.title),1)]),_:2},1024)):(r(),b(l(Qe),{key:1,"as-child":""},{default:p(()=>[h(l(v),{href:c.href??"#"},{default:p(()=>[M(x(c.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),g!==a.breadcrumbs.length-1?(r(),b(l(Ze),{key:0})):_("",!0)],64))),128))]),_:1})]),_:1}))}}),ss={class:"flex h-screen bg-gray-100"},ts={class:"flex-1 flex flex-col overflow-hidden"},rs={class:"bg-white shadow-sm border-b border-gray-200"},as={class:"px-4 py-2"},os={class:"flex items-center justify-between"},ns={class:"flex items-center space-x-3"},is={class:"text-sm text-gray-700"},ls={class:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100"},us={__name:"AppSidebarLayout",props:{breadcrumbs:{type:Array,default:()=>[]}},setup(u){var c;const o=(c=oe().props.auth)==null?void 0:c.user;return(g,C)=>{var m;return r(),i("div",ss,[h(Je,{user:l(o)},null,8,["user"]),t("div",ts,[t("header",rs,[t("div",as,[t("div",os,[t("div",null,[u.breadcrumbs.length>0?(r(),b(es,{key:0,breadcrumbs:u.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)]),t("div",ns,[t("span",is,"Welcome, "+x((m=l(o))==null?void 0:m.name),1)])])])]),t("main",ls,[k(g.$slots,"default")])])])}}},fs=w({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(u){return(a,o)=>(r(),b(us,{breadcrumbs:a.breadcrumbs},{default:p(()=>[k(a.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{fs as _};
