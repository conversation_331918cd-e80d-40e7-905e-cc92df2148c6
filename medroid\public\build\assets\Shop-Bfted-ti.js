import{r as u,c as R,w as J,e as o,g as v,f as l,i as e,t as i,x as X,y as M,z as E,F as T,s as q,p as N,m as S,a as F,W as K,o as Y,j as Q,u as Z,q as ee,l as te,A as se}from"./vendor-bzEMSiaZ.js";import{_ as oe}from"./AppLayout.vue_vue_type_script_setup_true_lang-ikY--3ik.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const le={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},ae={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},ie={class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"},re={class:"flex items-center justify-between mb-6"},ne={class:"text-sm text-gray-600"},de={key:0,class:"mb-4 p-4 bg-green-50 border border-green-200 rounded-lg"},ce={class:"text-green-800"},ue={key:1,class:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg"},me={class:"text-red-800"},pe={key:0,class:"p-4 bg-purple-50 rounded-lg"},ve={class:"flex items-center justify-between"},ge={class:"font-medium text-purple-900"},xe={class:"text-sm text-purple-700"},ye={class:"text-right"},fe={class:"font-bold text-purple-900"},be={key:0,class:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"},_e=["min"],he={key:1},we={key:0,class:"grid grid-cols-2 md:grid-cols-3 gap-2"},ke=["onClick"],$e={key:1,class:"text-center py-4 text-gray-500"},Se={class:"flex items-center justify-end space-x-4 pt-4 border-t"},Ce=["disabled"],Pe={key:0,class:"fas fa-spinner fa-spin mr-2"},je={key:1,class:"fas fa-calendar-plus mr-2"},Be={__name:"AppointmentBookingModal",props:{isOpen:{type:Boolean,default:!1},service:{type:Object,default:null},provider:{type:Object,default:null}},emits:["close","booked"],setup(b,{emit:G}){const g=b,h=G,n=u({date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1}),_=u(!1),w=u([]),j=u(""),p=u(""),x=u(""),C=R(()=>n.value.date&&n.value.time_slot.start_time&&n.value.time_slot.end_time&&n.value.reason.trim()),k=()=>{h("close"),D()},D=()=>{n.value={date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1},j.value="",w.value=[],p.value="",x.value=""},B=async d=>{if(!(!d||!g.provider))try{const r=await F.get(`/api/providers/${g.provider.id}/available-slots`,{params:{date:d}});w.value=r.data.slots||[]}catch(r){console.error("Error loading available slots:",r),w.value=[]}},I=d=>{n.value.time_slot={start_time:d.start_time,end_time:d.end_time}},O=async()=>{var d,r,P,A;if(C.value){_.value=!0,p.value="",x.value="";try{const $={provider_id:g.provider.id,service_id:(d=g.service)==null?void 0:d.id,date:n.value.date,time_slot:n.value.time_slot,reason:n.value.reason,notes:n.value.notes,is_telemedicine:n.value.is_telemedicine||((r=g.service)==null?void 0:r.is_telemedicine)||!1},c=await F.post("/api/appointments",$);c.data.appointment&&(c.data.payment_required?K.visit(`/appointments/${c.data.appointment.id}/payment`):(x.value="Appointment booked successfully!",h("booked",c.data.appointment),setTimeout(()=>{k(),K.visit("/appointments")},2e3)))}catch($){console.error("Error booking appointment:",$),p.value=((A=(P=$.response)==null?void 0:P.data)==null?void 0:A.message)||"Failed to book appointment. Please try again."}finally{_.value=!1}}};J(()=>n.value.date,d=>{d&&B(d)}),J(()=>g.service,d=>{d&&(n.value.is_telemedicine=d.is_telemedicine||!1)});const W=R(()=>new Date().toISOString().split("T")[0]),V=d=>new Date(`2000-01-01T${d}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return(d,r)=>{var P,A,$;return b.isOpen?(l(),o("div",le,[e("div",ae,[e("div",{class:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:k}),e("div",ie,[e("div",re,[e("div",null,[r[3]||(r[3]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Book Appointment",-1)),e("p",ne,i((P=b.service)==null?void 0:P.name)+" with Dr. "+i(($=(A=b.provider)==null?void 0:A.user)==null?void 0:$.name),1)]),e("button",{onClick:k,class:"text-gray-400 hover:text-gray-600"},r[4]||(r[4]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),x.value?(l(),o("div",de,[e("p",ce,i(x.value),1)])):v("",!0),p.value?(l(),o("div",ue,[e("p",me,i(p.value),1)])):v("",!0),e("form",{onSubmit:X(O,["prevent"]),class:"space-y-6"},[b.service?(l(),o("div",pe,[e("div",ve,[e("div",null,[e("h4",ge,i(b.service.name),1),e("p",xe,i(b.service.duration)+" minutes",1)]),e("div",ye,[e("p",fe,"$"+i(b.service.price),1),b.service.is_telemedicine?(l(),o("span",be," Video Call ")):v("",!0)])])])):v("",!0),e("div",null,[r[5]||(r[5]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Select Date",-1)),M(e("input",{"onUpdate:modelValue":r[0]||(r[0]=c=>n.value.date=c),type:"date",min:W.value,class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:""},null,8,_e),[[E,n.value.date]])]),n.value.date?(l(),o("div",he,[r[6]||(r[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Available Time Slots",-1)),w.value.length>0?(l(),o("div",we,[(l(!0),o(T,null,q(w.value,c=>(l(),o("button",{key:`${c.start_time}-${c.end_time}`,type:"button",onClick:H=>I(c),class:N(["p-3 text-sm border rounded-lg transition-colors",n.value.time_slot.start_time===c.start_time?"border-purple-500 bg-purple-50 text-purple-700":"border-gray-300 hover:border-purple-300 hover:bg-purple-50"])},i(V(c.start_time))+" - "+i(V(c.end_time)),11,ke))),128))])):(l(),o("div",$e," No available slots for this date "))])):v("",!0),e("div",null,[r[7]||(r[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Reason for Visit *",-1)),M(e("textarea",{"onUpdate:modelValue":r[1]||(r[1]=c=>n.value.reason=c),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Please describe your symptoms or reason for the appointment...",required:""},null,512),[[E,n.value.reason]])]),e("div",null,[r[8]||(r[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Additional Notes (Optional)",-1)),M(e("textarea",{"onUpdate:modelValue":r[2]||(r[2]=c=>n.value.notes=c),rows:"2",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Any additional information you'd like to share..."},null,512),[[E,n.value.notes]])]),e("div",Se,[e("button",{type:"button",onClick:k,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"}," Cancel "),e("button",{type:"submit",disabled:!C.value||_.value,class:N(["px-6 py-2 rounded-lg font-medium transition-colors",C.value&&!_.value?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[_.value?(l(),o("i",Pe)):(l(),o("i",je)),S(" "+i(_.value?"Booking...":"Book Appointment"),1)],10,Ce)])],32)])])])):v("",!0)}}},Ae={class:"py-12"},Ne={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},De={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ve={class:"p-6 bg-white border-b border-gray-200"},ze={class:"flex items-center justify-between"},Le={class:"flex mt-4 bg-gray-100 rounded-lg p-1 max-w-md"},Te={class:"flex items-center space-x-4"},Me={class:"relative"},Oe={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ue={key:1},Ee={key:0,class:"mb-6"},qe={key:1,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Fe={class:"p-4"},Ge={class:"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3"},Ie=["onClick"],We={class:"text-lg mb-1"},He={class:"text-xs font-medium leading-tight"},Qe={key:2,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Re={class:"p-6"},Je={class:"flex items-center justify-between mb-6"},Ke={key:0,class:"text-center py-12"},Xe={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ye=["onClick"],Ze={class:"p-6"},et={class:"flex items-center justify-between mb-4"},tt={class:"flex items-center space-x-3"},st={class:"flex-1"},ot={class:"text-lg font-semibold text-gray-900"},lt={class:"text-sm text-purple-600 font-medium"},at={class:"text-right"},it={class:"text-xs text-gray-500"},rt={class:"space-y-2 mb-4"},nt={key:0,class:"text-sm text-gray-600 leading-relaxed"},dt={class:"line-clamp-3 break-words"},ct={class:"flex items-center text-sm text-gray-500"},ut={class:"flex items-center text-sm text-gray-500"},mt={class:"capitalize"},pt={class:"flex items-center text-sm text-gray-500"},vt={key:1,class:"flex items-center text-sm text-gray-500"},gt={key:0,class:"mb-4"},xt={class:"flex items-center justify-between text-sm"},yt={class:"font-semibold text-purple-600"},ft={key:2,class:"text-center py-12"},bt={key:3,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},_t={class:"p-6"},ht={class:"border-b border-gray-200 pb-6 mb-6"},wt={class:"flex items-center space-x-4"},kt={class:"flex-1"},$t={class:"text-2xl font-bold text-gray-900"},St={class:"text-purple-600 font-medium"},Ct={class:"flex items-center mt-2 space-x-4 text-sm text-gray-500"},Pt={key:0,class:"mt-4"},jt={class:"text-gray-600 leading-relaxed break-words"},Bt={key:0,class:"text-center py-12"},At={key:1},Nt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Dt={class:"p-6"},Vt={class:"flex items-center justify-between mb-3"},zt={class:"text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full"},Lt={key:0,class:"text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full"},Tt={class:"text-lg font-semibold text-gray-900 mb-2"},Mt={class:"text-gray-600 text-sm mb-4 line-clamp-3"},Ot={class:"space-y-2 mb-4"},Ut={class:"flex items-center justify-between text-sm"},Et={class:"font-medium"},qt={class:"flex items-center justify-between text-sm"},Ft={class:"font-bold text-purple-600 text-lg"},Gt=["onClick","disabled"],It={key:2,class:"text-center py-12"},Yt={__name:"Shop",setup(b){const G=[{title:"Shop",href:"/shop"}],g=u(""),h=u("all"),n=u("name"),_=u(!1),w=u(!1),j=u("services"),p=u(null),x=u(!1),C=u(!1),k=u(null),D=u([]),B=u([]),I=u([{id:"all",name:"All Specializations",icon:"🏥"},{id:"general-practice",name:"General Practice",icon:"👨‍⚕️"},{id:"cardiology",name:"Cardiology",icon:"❤️"},{id:"dermatology",name:"Dermatology",icon:"🧴"},{id:"psychiatry",name:"Psychiatry",icon:"🧠"},{id:"pediatrics",name:"Pediatrics",icon:"👶"},{id:"orthopedics",name:"Orthopedics",icon:"🦴"},{id:"gynecology",name:"Gynecology",icon:"👩‍⚕️"},{id:"neurology",name:"Neurology",icon:"🧠"},{id:"oncology",name:"Oncology",icon:"🎗️"},{id:"ophthalmology",name:"Ophthalmology",icon:"👁️"}]),O=async()=>{_.value=!0;try{const a=await F.get("/api/providers/public");D.value=a.data.providers||[]}catch(a){console.error("Error loading providers:",a),D.value=[]}finally{_.value=!1}},W=async a=>{w.value=!0;try{const t=await F.get(`/api/providers/${a}/services`);B.value=t.data.services||[]}catch(t){console.error("Error loading provider services:",t),B.value=[]}finally{w.value=!1}},V=R(()=>{let a=D.value;if(h.value!=="all"){const t=h.value.replace("-"," ");a=a.filter(m=>{var f;const y=((f=m.specialization)==null?void 0:f.toLowerCase())||"";return y.includes(t)||y.includes("general")&&t.includes("general")||y===h.value})}if(g.value.trim()){const t=g.value.toLowerCase();a=a.filter(m=>{var y,f,z,L;return((f=(y=m.user)==null?void 0:y.name)==null?void 0:f.toLowerCase().includes(t))||((z=m.specialization)==null?void 0:z.toLowerCase().includes(t))||((L=m.bio)==null?void 0:L.toLowerCase().includes(t))})}switch(n.value){case"experience":a.sort((t,m)=>(m.years_of_experience||0)-(t.years_of_experience||0));break;case"name":default:a.sort((t,m)=>{var y,f;return(((y=t.user)==null?void 0:y.name)||"").localeCompare(((f=m.user)==null?void 0:f.name)||"")});break}return a}),d=a=>{if(!a.weekly_availability)return"Not specified";const t=Object.keys(a.weekly_availability).filter(m=>a.weekly_availability[m]&&a.weekly_availability[m].length>0);return t.length===0?"Not available":t.length===7?"Every day":t.map(m=>m.charAt(0).toUpperCase()+m.slice(1)).join(", ")},r=a=>!a.practice_locations||a.practice_locations.length===0?"Location not specified":a.practice_locations[0],P=a=>{j.value=a,h.value="all",g.value="",p.value=null,x.value=!1,a==="services"&&O()},A=async a=>{p.value=a,x.value=!0,await W(a.id)},$=()=>{p.value=null,x.value=!1,B.value=[]},c=a=>{k.value=a,C.value=!0},H=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a);return Y(()=>{O()}),(a,t)=>(l(),o(T,null,[Q(Z(ee),{title:"Shop - Medroid"}),Q(oe,{breadcrumbs:G},{default:te(()=>{var m,y,f,z,L;return[e("div",Ae,[e("div",Ne,[e("div",De,[e("div",Ve,[e("div",ze,[e("div",null,[t[7]||(t[7]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Health Shop",-1)),t[8]||(t[8]=e("p",{class:"text-gray-600 mt-1"},"Browse healthcare providers and book medical services",-1)),e("div",Le,[e("button",{onClick:t[0]||(t[0]=s=>P("products")),class:N(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors relative",j.value==="products"?"bg-white text-blue-600 shadow-sm":"text-gray-400 cursor-not-allowed"]),disabled:""},t[6]||(t[6]=[S(" 🛒 Products "),e("span",{class:"absolute -top-1 -right-1 bg-yellow-500 text-white text-xs px-1 rounded-full"}," Soon ",-1)]),2),e("button",{onClick:t[1]||(t[1]=s=>P("services")),class:N(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors",j.value==="services"?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🩺 Services ",2)])]),e("div",Te,[e("div",Me,[M(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>g.value=s),type:"text",placeholder:"Search providers...",class:"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"},null,512),[[E,g.value]]),t[9]||(t[9]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),t[10]||(t[10]=e("div",{class:"text-sm text-gray-500 bg-yellow-50 px-3 py-2 rounded-lg border border-yellow-200"},[e("i",{class:"fas fa-info-circle mr-1"}),S(" Products coming soon ")],-1))])])])]),j.value==="products"?(l(),o("div",Oe,t[11]||(t[11]=[e("div",{class:"p-12 text-center"},[e("div",{class:"text-6xl mb-4"},"🛒"),e("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Products Coming Soon"),e("p",{class:"text-gray-600 mb-6"},"We're working hard to bring you a comprehensive selection of health products. Stay tuned!"),e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto"},[e("p",{class:"text-yellow-800 text-sm"},[e("i",{class:"fas fa-clock mr-2"}),S(" In the meantime, explore our healthcare services and book appointments with qualified providers. ")])])],-1)]))):v("",!0),j.value==="services"?(l(),o("div",Ue,[x.value?(l(),o("div",Ee,[e("button",{onClick:$,class:"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},t[12]||(t[12]=[e("i",{class:"fas fa-arrow-left mr-2"},null,-1),S(" Back to Providers ")]))])):v("",!0),x.value?v("",!0):(l(),o("div",qe,[e("div",Fe,[t[13]||(t[13]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-3"},"Browse by Specialization",-1)),e("div",Ge,[(l(!0),o(T,null,q(I.value,s=>(l(),o("button",{key:s.id,onClick:U=>h.value=s.id,class:N(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",h.value===s.id?"border-purple-500 bg-purple-50 text-purple-700 shadow-sm":"border-gray-200 hover:border-purple-300 text-gray-700 hover:bg-gray-50"])},[e("div",We,i(s.icon),1),e("div",He,i(s.name),1)],10,Ie))),128))])])])),x.value?(l(),o("div",bt,[e("div",_t,[e("div",ht,[e("div",wt,[t[27]||(t[27]=e("div",{class:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-2xl"})],-1)),e("div",kt,[e("h2",$t," Dr. "+i(((y=(m=p.value)==null?void 0:m.user)==null?void 0:y.name)||"Provider"),1),e("p",St,i((f=p.value)==null?void 0:f.specialization),1),e("div",Ct,[e("span",null,[t[25]||(t[25]=e("i",{class:"fas fa-clock mr-1"},null,-1)),S(i(((z=p.value)==null?void 0:z.years_of_experience)||0)+" years exp.",1)]),e("span",null,[t[26]||(t[26]=e("i",{class:"fas fa-map-marker-alt mr-1"},null,-1)),S(i(r(p.value)),1)])])])]),(L=p.value)!=null&&L.bio?(l(),o("div",Pt,[e("p",jt,i(p.value.bio),1)])):v("",!0)]),w.value?(l(),o("div",Bt,t[28]||(t[28]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading services...",-1)]))):B.value.length>0?(l(),o("div",At,[t[32]||(t[32]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Available Services",-1)),e("div",Nt,[(l(!0),o(T,null,q(B.value,s=>(l(),o("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",Dt,[e("div",Vt,[e("span",zt,i(s.category||"Service"),1),s.is_telemedicine?(l(),o("span",Lt," Video Call ")):v("",!0)]),e("h4",Tt,i(s.name),1),e("p",Mt,i(s.description),1),e("div",Ot,[e("div",Ut,[t[29]||(t[29]=e("span",{class:"text-gray-500"},"Duration:",-1)),e("span",Et,i(s.duration)+" minutes",1)]),e("div",qt,[t[30]||(t[30]=e("span",{class:"text-gray-500"},"Price:",-1)),e("span",Ft,i(H(s.price)),1)])]),e("button",{onClick:U=>c(s),disabled:!s.active,class:N(["w-full py-2 px-4 rounded-lg transition-colors font-medium",s.active?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[t[31]||(t[31]=e("i",{class:"fas fa-calendar-plus mr-2"},null,-1)),S(" "+i(s.active?"Book Appointment":"Unavailable"),1)],10,Gt)])]))),128))])])):(l(),o("div",It,t[33]||(t[33]=[e("i",{class:"fas fa-stethoscope text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No services available",-1),e("p",{class:"text-gray-500"},"This provider hasn't listed any services yet.",-1)])))])])):(l(),o("div",Qe,[e("div",Re,[e("div",Je,[t[15]||(t[15]=e("h2",{class:"text-lg font-semibold text-gray-900"},"Healthcare Providers",-1)),M(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>n.value=s),class:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"},t[14]||(t[14]=[e("option",{value:"name"},"Sort by Name",-1),e("option",{value:"experience"},"Experience",-1)]),512),[[se,n.value]])]),_.value?(l(),o("div",Ke,t[16]||(t[16]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading providers...",-1)]))):V.value.length>0?(l(),o("div",Xe,[(l(!0),o(T,null,q(V.value,s=>{var U;return l(),o("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer",onClick:Wt=>A(s)},[e("div",Ze,[e("div",et,[e("div",tt,[t[17]||(t[17]=e("div",{class:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-xl"})],-1)),e("div",st,[e("h3",ot," Dr. "+i(((U=s.user)==null?void 0:U.name)||"Provider"),1),e("p",lt,i(s.specialization||"General Practice"),1)])]),e("div",at,[e("p",it,i(s.years_of_experience||0)+" years exp. ",1)])]),e("div",rt,[s.bio?(l(),o("div",nt,[e("p",dt,i(s.bio),1)])):v("",!0),e("div",ct,[t[18]||(t[18]=e("i",{class:"fas fa-map-marker-alt mr-2 text-gray-400"},null,-1)),e("span",null,i(r(s)),1)]),e("div",ut,[t[19]||(t[19]=e("i",{class:"fas fa-venus-mars mr-2 text-gray-400"},null,-1)),e("span",mt,i(s.gender||"Not specified"),1)]),e("div",pt,[t[20]||(t[20]=e("i",{class:"fas fa-calendar mr-2 text-gray-400"},null,-1)),e("span",null,i(d(s)),1)]),s.languages&&s.languages.length>0?(l(),o("div",vt,[t[21]||(t[21]=e("i",{class:"fas fa-language mr-2 text-gray-400"},null,-1)),e("span",null,i(s.languages.slice(0,2).join(", "))+i(s.languages.length>2?"...":""),1)])):v("",!0)]),s.pricing?(l(),o("div",gt,[e("div",xt,[t[22]||(t[22]=e("span",{class:"text-gray-600"},"Consultation:",-1)),e("span",yt,i(H(s.pricing.consultation||0)),1)])])):v("",!0),t[23]||(t[23]=e("button",{class:"w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium"},[e("i",{class:"fas fa-eye mr-2"}),S(" View Services ")],-1))])],8,Ye)}),128))])):(l(),o("div",ft,t[24]||(t[24]=[e("i",{class:"fas fa-user-md text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No providers found",-1),e("p",{class:"text-gray-500"},"Try adjusting your search or filter criteria.",-1)])))])]))])):v("",!0)])]),Q(Be,{"is-open":C.value,service:k.value,provider:p.value,onClose:t[4]||(t[4]=s=>{C.value=!1,k.value=null}),onBooked:t[5]||(t[5]=s=>{C.value=!1,k.value=null})},null,8,["is-open","service","provider"])]}),_:1})],64))}};export{Yt as default};
