import{d as v,r as f,e as a,f as s,i as o,g as m,I as y,p as x,m as d}from"./vendor-bzEMSiaZ.js";import{_ as w}from"./_plugin-vue_export-helper-DlAUqK2U.js";const k={class:"space-y-4"},_={class:"relative"},V={class:"bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200"},C={key:0,class:"flex items-center justify-between px-4 py-2 border-b border-gray-100"},L={class:"relative"},B=["value","placeholder","disabled"],I={class:"flex items-center justify-between px-4 pb-3"},M=["disabled"],T={key:0,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24"},z={key:1,class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},j=v({__name:"ChatInput",props:{modelValue:{},placeholder:{default:"Type your message..."},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1},showTools:{type:Boolean,default:!0},showVersion:{type:Boolean,default:!1}},emits:["update:modelValue","send","keydown"],setup(N,{expose:i,emit:u}){const r=u,n=f(null),c=e=>e&&e.replace(/([.!?]\s*)([a-z])/g,(t,l,b)=>l+b.toUpperCase()).replace(/^[a-z]/,t=>t.toUpperCase()),p=e=>{const t=e.target,l=c(t.value);r("update:modelValue",l)},g=e=>{r("keydown",e)},h=()=>{r("send")};return i({focus:()=>{n.value&&n.value.focus()}}),(e,t)=>(s(),a("div",k,[o("div",_,[o("div",V,[e.showTools?(s(),a("div",C,t[0]||(t[0]=[o("div",{class:"flex items-center space-x-2"},[o("button",{class:"flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-150"},[o("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 008 10.172V5L8 4z"})]),o("span",null,"Tools")])],-1)]))):m("",!0),o("div",L,[o("textarea",{ref_key:"chatInputRef",ref:n,value:e.modelValue,onInput:p,onKeydown:g,placeholder:e.placeholder,class:"w-full px-4 py-4 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed min-h-[60px] max-h-[200px]",rows:"1",disabled:e.disabled||e.isLoading},null,40,B),o("div",I,[t[2]||(t[2]=y('<div class="flex items-center space-x-2" data-v-6d7bb639><button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150" data-v-6d7bb639><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6d7bb639><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" data-v-6d7bb639></path></svg></button><button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-150" data-v-6d7bb639><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6d7bb639><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" data-v-6d7bb639></path></svg></button></div>',1)),o("button",{onClick:h,disabled:e.isLoading||!e.modelValue.trim(),class:x(["p-2 rounded-lg transition-colors duration-150",e.modelValue.trim()&&!e.isLoading?"bg-medroid-orange text-white hover:bg-medroid-orange/90":"bg-gray-200 text-gray-500 cursor-not-allowed"])},[e.isLoading?(s(),a("div",z)):(s(),a("svg",T,t[1]||(t[1]=[o("path",{d:"M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z"},null,-1)])))],10,M)])])])]),t[3]||(t[3]=o("p",{class:"text-xs text-gray-500 text-center leading-relaxed px-4"},[d(" Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our "),o("button",{class:"text-gray-600 hover:text-gray-800 underline underline-offset-2"},"Terms of Service"),d(" and "),o("button",{class:"text-gray-600 hover:text-gray-800 underline underline-offset-2"},"Privacy Policy"),d(". ")],-1))]))}}),K=w(j,[["__scopeId","data-v-6d7bb639"]]);export{K as C};
