<script setup>
import { ref, computed, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import axios from 'axios'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    service: {
        type: Object,
        default: null
    },
    provider: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['close', 'booked'])

// Form data
const form = ref({
    date: '',
    time_slot: {
        start_time: '',
        end_time: ''
    },
    reason: '',
    notes: '',
    is_telemedicine: false
})

const loading = ref(false)
const availableSlots = ref([])
const selectedDate = ref('')
const errorMessage = ref('')
const successMessage = ref('')

// Computed
const isFormValid = computed(() => {
    return form.value.date && 
           form.value.time_slot.start_time && 
           form.value.time_slot.end_time && 
           form.value.reason.trim()
})

// Methods
const closeModal = () => {
    emit('close')
    resetForm()
}

const resetForm = () => {
    form.value = {
        date: '',
        time_slot: {
            start_time: '',
            end_time: ''
        },
        reason: '',
        notes: '',
        is_telemedicine: false
    }
    selectedDate.value = ''
    availableSlots.value = []
    errorMessage.value = ''
    successMessage.value = ''
}

const loadAvailableSlots = async (date) => {
    if (!date || !props.provider) return
    
    try {
        const response = await axios.get(`/api/providers/${props.provider.id}/available-slots`, {
            params: { date }
        })
        availableSlots.value = response.data.slots || []
    } catch (error) {
        console.error('Error loading available slots:', error)
        availableSlots.value = []
    }
}

const selectTimeSlot = (slot) => {
    form.value.time_slot = {
        start_time: slot.start_time,
        end_time: slot.end_time
    }
}

const bookAppointment = async () => {
    if (!isFormValid.value) return
    
    loading.value = true
    errorMessage.value = ''
    successMessage.value = ''
    
    try {
        const appointmentData = {
            provider_id: props.provider.id,
            service_id: props.service?.id,
            date: form.value.date,
            time_slot: form.value.time_slot,
            reason: form.value.reason,
            notes: form.value.notes,
            is_telemedicine: form.value.is_telemedicine || props.service?.is_telemedicine || false
        }
        
        const response = await axios.post('/save-appointment', appointmentData)
        
        if (response.data.appointment) {
            // Redirect to payment if needed
            if (response.data.payment_required) {
                router.visit(`/appointments/${response.data.appointment.id}/payment`)
            } else {
                successMessage.value = 'Appointment booked successfully!'
                emit('booked', response.data.appointment)
                setTimeout(() => {
                    closeModal()
                    router.visit('/appointments')
                }, 2000)
            }
        }
    } catch (error) {
        console.error('Error booking appointment:', error)
        errorMessage.value = error.response?.data?.message || 'Failed to book appointment. Please try again.'
    } finally {
        loading.value = false
    }
}

// Watch for date changes
watch(() => form.value.date, (newDate) => {
    if (newDate) {
        loadAvailableSlots(newDate)
    }
})

// Watch for service changes
watch(() => props.service, (newService) => {
    if (newService) {
        form.value.is_telemedicine = newService.is_telemedicine || false
    }
})

// Get minimum date (today)
const minDate = computed(() => {
    const today = new Date()
    return today.toISOString().split('T')[0]
})

// Format time for display
const formatTime = (time) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    })
}
</script>

<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="closeModal"></div>

            <!-- Modal -->
            <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
                <!-- Header -->
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Book Appointment</h3>
                        <p class="text-sm text-gray-600">
                            {{ service?.name }} with Dr. {{ provider?.user?.name }}
                        </p>
                    </div>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Success/Error Messages -->
                <div v-if="successMessage" class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p class="text-green-800">{{ successMessage }}</p>
                </div>

                <div v-if="errorMessage" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-800">{{ errorMessage }}</p>
                </div>

                <!-- Form -->
                <form @submit.prevent="bookAppointment" class="space-y-6">
                    <!-- Service Info -->
                    <div v-if="service" class="p-4 bg-purple-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-purple-900">{{ service.name }}</h4>
                                <p class="text-sm text-purple-700">{{ service.duration }} minutes</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-purple-900">${{ service.price }}</p>
                                <span v-if="service.is_telemedicine" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    Video Call
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Date Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Date</label>
                        <input
                            v-model="form.date"
                            type="date"
                            :min="minDate"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            required
                        />
                    </div>

                    <!-- Time Slots -->
                    <div v-if="form.date">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Available Time Slots</label>
                        <div v-if="availableSlots.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-2">
                            <button
                                v-for="slot in availableSlots"
                                :key="`${slot.start_time}-${slot.end_time}`"
                                type="button"
                                @click="selectTimeSlot(slot)"
                                :class="[
                                    'p-3 text-sm border rounded-lg transition-colors',
                                    form.time_slot.start_time === slot.start_time
                                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                                        : 'border-gray-300 hover:border-purple-300 hover:bg-purple-50'
                                ]"
                            >
                                {{ formatTime(slot.start_time) }} - {{ formatTime(slot.end_time) }}
                            </button>
                        </div>
                        <div v-else class="text-center py-4 text-gray-500">
                            No available slots for this date
                        </div>
                    </div>

                    <!-- Reason -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Visit *</label>
                        <textarea
                            v-model="form.reason"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            placeholder="Please describe your symptoms or reason for the appointment..."
                            required
                        ></textarea>
                    </div>

                    <!-- Additional Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Additional Notes (Optional)</label>
                        <textarea
                            v-model="form.notes"
                            rows="2"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            placeholder="Any additional information you'd like to share..."
                        ></textarea>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-end space-x-4 pt-4 border-t">
                        <button
                            type="button"
                            @click="closeModal"
                            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="!isFormValid || loading"
                            :class="[
                                'px-6 py-2 rounded-lg font-medium transition-colors',
                                isFormValid && !loading
                                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            ]"
                        >
                            <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
                            <i v-else class="fas fa-calendar-plus mr-2"></i>
                            {{ loading ? 'Booking...' : 'Book Appointment' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>
