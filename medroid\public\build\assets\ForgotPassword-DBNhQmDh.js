import{d as u,R as _,v as n,f as m,l as r,j as a,e as c,g as d,i as o,u as s,q as g,t as x,x as w,m as l}from"./vendor-bzEMSiaZ.js";import{_ as v}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";import{_ as y}from"./TextLink.vue_vue_type_script_setup_true_lang-EizaLhrD.js";import{_ as k}from"./index-Oq2yf1P1.js";import{_ as V,a as $}from"./Label.vue_vue_type_script_setup_true_lang-B-E--Xd1.js";import{L as b,_ as C}from"./AuthLayout.vue_vue_type_script_setup_true_lang-CXnZV5tQ.js";import"./Primitive-CNCx3Yln.js";import"./index-CLsNIGVi.js";import"./createLucideIcon-BJGbtoZV.js";const B={key:0,class:"mb-4 text-center text-sm font-medium text-green-600"},E={class:"space-y-6"},N={class:"grid gap-2"},F={class:"my-6 flex items-center justify-start"},h={class:"space-x-1 text-center text-sm text-muted-foreground"},U=u({__name:"ForgotPassword",props:{status:{}},setup(j){const t=_({email:""}),p=()=>{t.post(route("password.email"))};return(i,e)=>(m(),n(C,{title:"Forgot password",description:"Enter your email to receive a password reset link"},{default:r(()=>[a(s(g),{title:"Forgot password"}),i.status?(m(),c("div",B,x(i.status),1)):d("",!0),o("div",E,[o("form",{onSubmit:w(p,["prevent"])},[o("div",N,[a(s(V),{for:"email"},{default:r(()=>e[1]||(e[1]=[l("Email address")])),_:1}),a(s($),{id:"email",type:"email",name:"email",autocomplete:"off",modelValue:s(t).email,"onUpdate:modelValue":e[0]||(e[0]=f=>s(t).email=f),autofocus:"",placeholder:"<EMAIL>"},null,8,["modelValue"]),a(v,{message:s(t).errors.email},null,8,["message"])]),o("div",F,[a(s(k),{class:"w-full",disabled:s(t).processing},{default:r(()=>[s(t).processing?(m(),n(s(b),{key:0,class:"h-4 w-4 animate-spin"})):d("",!0),e[2]||(e[2]=l(" Email password reset link "))]),_:1},8,["disabled"])])],32),o("div",h,[e[4]||(e[4]=o("span",null,"Or, return to",-1)),a(y,{href:i.route("login")},{default:r(()=>e[3]||(e[3]=[l("log in")])),_:1},8,["href"])])])]),_:1}))}});export{U as default};
