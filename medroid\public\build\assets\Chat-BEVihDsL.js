import{E as Dt,r as L,w as Ge,e as D,g as W,f as P,i as o,x as ye,I as We,F as ie,s as ae,p as oe,t as F,y as Ke,z as Pt,m as Mt,a as Q,B as ne,o as $t,j as he,u as Nt,q as Lt,l as qt,A as Xe}from"./vendor-bzEMSiaZ.js";import{_ as jt}from"./AppLayout.vue_vue_type_script_setup_true_lang-ikY--3ik.js";import{C as Ut}from"./ChatInput-Cgkfazpi.js";import{M as Ft}from"./MedroidLogo-c6q-kzU_.js";import{_ as zt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";var ce={},_e,Ze;function Vt(){return Ze||(Ze=1,_e=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),_e}var ke={},se={},et;function le(){if(et)return se;et=1;let s;const r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return se.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},se.getSymbolTotalCodewords=function(n){return r[n]},se.getBCHDigit=function(a){let n=0;for(;a!==0;)n++,a>>>=1;return n},se.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');s=n},se.isKanjiModeEnabled=function(){return typeof s<"u"},se.toSJIS=function(n){return s(n)},se}var Ee={},tt;function Ye(){return tt||(tt=1,function(s){s.L={bit:1},s.M={bit:0},s.Q={bit:3},s.H={bit:2};function r(a){if(typeof a!="string")throw new Error("Param is not a string");switch(a.toLowerCase()){case"l":case"low":return s.L;case"m":case"medium":return s.M;case"q":case"quartile":return s.Q;case"h":case"high":return s.H;default:throw new Error("Unknown EC Level: "+a)}}s.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},s.from=function(n,e){if(s.isValid(n))return n;try{return r(n)}catch{return e}}}(Ee)),Ee}var Te,nt;function Ht(){if(nt)return Te;nt=1;function s(){this.buffer=[],this.length=0}return s.prototype={get:function(r){const a=Math.floor(r/8);return(this.buffer[a]>>>7-r%8&1)===1},put:function(r,a){for(let n=0;n<a;n++)this.putBit((r>>>a-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(r){const a=Math.floor(this.length/8);this.buffer.length<=a&&this.buffer.push(0),r&&(this.buffer[a]|=128>>>this.length%8),this.length++}},Te=s,Te}var Me,ot;function Ot(){if(ot)return Me;ot=1;function s(r){if(!r||r<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=r,this.data=new Uint8Array(r*r),this.reservedBit=new Uint8Array(r*r)}return s.prototype.set=function(r,a,n,e){const t=r*this.size+a;this.data[t]=n,e&&(this.reservedBit[t]=!0)},s.prototype.get=function(r,a){return this.data[r*this.size+a]},s.prototype.xor=function(r,a,n){this.data[r*this.size+a]^=n},s.prototype.isReserved=function(r,a){return this.reservedBit[r*this.size+a]},Me=s,Me}var Se={},rt;function Kt(){return rt||(rt=1,function(s){const r=le().getSymbolSize;s.getRowColCoords=function(n){if(n===1)return[];const e=Math.floor(n/7)+2,t=r(n),i=t===145?26:Math.ceil((t-13)/(2*e-2))*2,u=[t-7];for(let l=1;l<e-1;l++)u[l]=u[l-1]-i;return u.push(6),u.reverse()},s.getPositions=function(n){const e=[],t=s.getRowColCoords(n),i=t.length;for(let u=0;u<i;u++)for(let l=0;l<i;l++)u===0&&l===0||u===0&&l===i-1||u===i-1&&l===0||e.push([t[u],t[l]]);return e}}(Se)),Se}var Ae={},st;function Yt(){if(st)return Ae;st=1;const s=le().getSymbolSize,r=7;return Ae.getPositions=function(n){const e=s(n);return[[0,0],[e-r,0],[0,e-r]]},Ae}var Be={},it;function Jt(){return it||(it=1,function(s){s.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const r={N1:3,N2:3,N3:40,N4:10};s.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},s.from=function(e){return s.isValid(e)?parseInt(e,10):void 0},s.getPenaltyN1=function(e){const t=e.size;let i=0,u=0,l=0,h=null,v=null;for(let C=0;C<t;C++){u=l=0,h=v=null;for(let w=0;w<t;w++){let m=e.get(C,w);m===h?u++:(u>=5&&(i+=r.N1+(u-5)),h=m,u=1),m=e.get(w,C),m===v?l++:(l>=5&&(i+=r.N1+(l-5)),v=m,l=1)}u>=5&&(i+=r.N1+(u-5)),l>=5&&(i+=r.N1+(l-5))}return i},s.getPenaltyN2=function(e){const t=e.size;let i=0;for(let u=0;u<t-1;u++)for(let l=0;l<t-1;l++){const h=e.get(u,l)+e.get(u,l+1)+e.get(u+1,l)+e.get(u+1,l+1);(h===4||h===0)&&i++}return i*r.N2},s.getPenaltyN3=function(e){const t=e.size;let i=0,u=0,l=0;for(let h=0;h<t;h++){u=l=0;for(let v=0;v<t;v++)u=u<<1&2047|e.get(h,v),v>=10&&(u===1488||u===93)&&i++,l=l<<1&2047|e.get(v,h),v>=10&&(l===1488||l===93)&&i++}return i*r.N3},s.getPenaltyN4=function(e){let t=0;const i=e.data.length;for(let l=0;l<i;l++)t+=e.data[l];return Math.abs(Math.ceil(t*100/i/5)-10)*r.N4};function a(n,e,t){switch(n){case s.Patterns.PATTERN000:return(e+t)%2===0;case s.Patterns.PATTERN001:return e%2===0;case s.Patterns.PATTERN010:return t%3===0;case s.Patterns.PATTERN011:return(e+t)%3===0;case s.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(t/3))%2===0;case s.Patterns.PATTERN101:return e*t%2+e*t%3===0;case s.Patterns.PATTERN110:return(e*t%2+e*t%3)%2===0;case s.Patterns.PATTERN111:return(e*t%3+(e+t)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}s.applyMask=function(e,t){const i=t.size;for(let u=0;u<i;u++)for(let l=0;l<i;l++)t.isReserved(l,u)||t.xor(l,u,a(e,l,u))},s.getBestMask=function(e,t){const i=Object.keys(s.Patterns).length;let u=0,l=1/0;for(let h=0;h<i;h++){t(h),s.applyMask(h,e);const v=s.getPenaltyN1(e)+s.getPenaltyN2(e)+s.getPenaltyN3(e)+s.getPenaltyN4(e);s.applyMask(h,e),v<l&&(l=v,u=h)}return u}}(Be)),Be}var we={},at;function St(){if(at)return we;at=1;const s=Ye(),r=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],a=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return we.getBlocksCount=function(e,t){switch(t){case s.L:return r[(e-1)*4+0];case s.M:return r[(e-1)*4+1];case s.Q:return r[(e-1)*4+2];case s.H:return r[(e-1)*4+3];default:return}},we.getTotalCodewordsCount=function(e,t){switch(t){case s.L:return a[(e-1)*4+0];case s.M:return a[(e-1)*4+1];case s.Q:return a[(e-1)*4+2];case s.H:return a[(e-1)*4+3];default:return}},we}var Re={},me={},lt;function Qt(){if(lt)return me;lt=1;const s=new Uint8Array(512),r=new Uint8Array(256);return function(){let n=1;for(let e=0;e<255;e++)s[e]=n,r[n]=e,n<<=1,n&256&&(n^=285);for(let e=255;e<512;e++)s[e]=s[e-255]}(),me.log=function(n){if(n<1)throw new Error("log("+n+")");return r[n]},me.exp=function(n){return s[n]},me.mul=function(n,e){return n===0||e===0?0:s[r[n]+r[e]]},me}var ut;function Gt(){return ut||(ut=1,function(s){const r=Qt();s.mul=function(n,e){const t=new Uint8Array(n.length+e.length-1);for(let i=0;i<n.length;i++)for(let u=0;u<e.length;u++)t[i+u]^=r.mul(n[i],e[u]);return t},s.mod=function(n,e){let t=new Uint8Array(n);for(;t.length-e.length>=0;){const i=t[0];for(let l=0;l<e.length;l++)t[l]^=r.mul(e[l],i);let u=0;for(;u<t.length&&t[u]===0;)u++;t=t.slice(u)}return t},s.generateECPolynomial=function(n){let e=new Uint8Array([1]);for(let t=0;t<n;t++)e=s.mul(e,new Uint8Array([1,r.exp(t)]));return e}}(Re)),Re}var Ie,dt;function Wt(){if(dt)return Ie;dt=1;const s=Gt();function r(a){this.genPoly=void 0,this.degree=a,this.degree&&this.initialize(this.degree)}return r.prototype.initialize=function(n){this.degree=n,this.genPoly=s.generateECPolynomial(this.degree)},r.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(n.length+this.degree);e.set(n);const t=s.mod(e,this.genPoly),i=this.degree-t.length;if(i>0){const u=new Uint8Array(this.degree);return u.set(t,i),u}return t},Ie=r,Ie}var De={},Pe={},$e={},ct;function At(){return ct||(ct=1,$e.isValid=function(r){return!isNaN(r)&&r>=1&&r<=40}),$e}var ee={},ft;function Bt(){if(ft)return ee;ft=1;const s="[0-9]+",r="[A-Z $%*+\\-./:]+";let a="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";a=a.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+a+`)(?:.|[\r
]))+`;ee.KANJI=new RegExp(a,"g"),ee.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),ee.BYTE=new RegExp(n,"g"),ee.NUMERIC=new RegExp(s,"g"),ee.ALPHANUMERIC=new RegExp(r,"g");const e=new RegExp("^"+a+"$"),t=new RegExp("^"+s+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return ee.testKanji=function(l){return e.test(l)},ee.testNumeric=function(l){return t.test(l)},ee.testAlphanumeric=function(l){return i.test(l)},ee}var gt;function ue(){return gt||(gt=1,function(s){const r=At(),a=Bt();s.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},s.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},s.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},s.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},s.MIXED={bit:-1},s.getCharCountIndicator=function(t,i){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!r.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?t.ccBits[0]:i<27?t.ccBits[1]:t.ccBits[2]},s.getBestModeForData=function(t){return a.testNumeric(t)?s.NUMERIC:a.testAlphanumeric(t)?s.ALPHANUMERIC:a.testKanji(t)?s.KANJI:s.BYTE},s.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},s.isValid=function(t){return t&&t.bit&&t.ccBits};function n(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return s.NUMERIC;case"alphanumeric":return s.ALPHANUMERIC;case"kanji":return s.KANJI;case"byte":return s.BYTE;default:throw new Error("Unknown mode: "+e)}}s.from=function(t,i){if(s.isValid(t))return t;try{return n(t)}catch{return i}}}(Pe)),Pe}var ht;function Xt(){return ht||(ht=1,function(s){const r=le(),a=St(),n=Ye(),e=ue(),t=At(),i=7973,u=r.getBCHDigit(i);function l(w,m,E){for(let B=1;B<=40;B++)if(m<=s.getCapacity(B,E,w))return B}function h(w,m){return e.getCharCountIndicator(w,m)+4}function v(w,m){let E=0;return w.forEach(function(B){const z=h(B.mode,m);E+=z+B.getBitsLength()}),E}function C(w,m){for(let E=1;E<=40;E++)if(v(w,E)<=s.getCapacity(E,m,e.MIXED))return E}s.from=function(m,E){return t.isValid(m)?parseInt(m,10):E},s.getCapacity=function(m,E,B){if(!t.isValid(m))throw new Error("Invalid QR Code version");typeof B>"u"&&(B=e.BYTE);const z=r.getSymbolTotalCodewords(m),T=a.getTotalCodewordsCount(m,E),I=(z-T)*8;if(B===e.MIXED)return I;const S=I-h(B,m);switch(B){case e.NUMERIC:return Math.floor(S/10*3);case e.ALPHANUMERIC:return Math.floor(S/11*2);case e.KANJI:return Math.floor(S/13);case e.BYTE:default:return Math.floor(S/8)}},s.getBestVersionForData=function(m,E){let B;const z=n.from(E,n.M);if(Array.isArray(m)){if(m.length>1)return C(m,z);if(m.length===0)return 1;B=m[0]}else B=m;return l(B.mode,B.getLength(),z)},s.getEncodedBits=function(m){if(!t.isValid(m)||m<7)throw new Error("Invalid QR Code version");let E=m<<12;for(;r.getBCHDigit(E)-u>=0;)E^=i<<r.getBCHDigit(E)-u;return m<<12|E}}(De)),De}var Ne={},mt;function Zt(){if(mt)return Ne;mt=1;const s=le(),r=1335,a=21522,n=s.getBCHDigit(r);return Ne.getEncodedBits=function(t,i){const u=t.bit<<3|i;let l=u<<10;for(;s.getBCHDigit(l)-n>=0;)l^=r<<s.getBCHDigit(l)-n;return(u<<10|l)^a},Ne}var Le={},qe,pt;function en(){if(pt)return qe;pt=1;const s=ue();function r(a){this.mode=s.NUMERIC,this.data=a.toString()}return r.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(n){let e,t,i;for(e=0;e+3<=this.data.length;e+=3)t=this.data.substr(e,3),i=parseInt(t,10),n.put(i,10);const u=this.data.length-e;u>0&&(t=this.data.substr(e),i=parseInt(t,10),n.put(i,u*3+1))},qe=r,qe}var je,vt;function tn(){if(vt)return je;vt=1;const s=ue(),r=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function a(n){this.mode=s.ALPHANUMERIC,this.data=n}return a.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let i=r.indexOf(this.data[t])*45;i+=r.indexOf(this.data[t+1]),e.put(i,11)}this.data.length%2&&e.put(r.indexOf(this.data[t]),6)},je=a,je}var Ue,yt;function nn(){if(yt)return Ue;yt=1;const s=ue();function r(a){this.mode=s.BYTE,typeof a=="string"?this.data=new TextEncoder().encode(a):this.data=new Uint8Array(a)}return r.getBitsLength=function(n){return n*8},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(a){for(let n=0,e=this.data.length;n<e;n++)a.put(this.data[n],8)},Ue=r,Ue}var Fe,wt;function on(){if(wt)return Fe;wt=1;const s=ue(),r=le();function a(n){this.mode=s.KANJI,this.data=n}return a.getBitsLength=function(e){return e*13},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(n){let e;for(e=0;e<this.data.length;e++){let t=r.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),n.put(t,13)}},Fe=a,Fe}var ze={exports:{}},bt;function rn(){return bt||(bt=1,function(s){var r={single_source_shortest_paths:function(a,n,e){var t={},i={};i[n]=0;var u=r.PriorityQueue.make();u.push(n,0);for(var l,h,v,C,w,m,E,B,z;!u.empty();){l=u.pop(),h=l.value,C=l.cost,w=a[h]||{};for(v in w)w.hasOwnProperty(v)&&(m=w[v],E=C+m,B=i[v],z=typeof i[v]>"u",(z||B>E)&&(i[v]=E,u.push(v,E),t[v]=h))}if(typeof e<"u"&&typeof i[e]>"u"){var T=["Could not find a path from ",n," to ",e,"."].join("");throw new Error(T)}return t},extract_shortest_path_from_predecessor_list:function(a,n){for(var e=[],t=n;t;)e.push(t),a[t],t=a[t];return e.reverse(),e},find_path:function(a,n,e){var t=r.single_source_shortest_paths(a,n,e);return r.extract_shortest_path_from_predecessor_list(t,e)},PriorityQueue:{make:function(a){var n=r.PriorityQueue,e={},t;a=a||{};for(t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);return e.queue=[],e.sorter=a.sorter||n.default_sorter,e},default_sorter:function(a,n){return a.cost-n.cost},push:function(a,n){var e={value:a,cost:n};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};s.exports=r}(ze)),ze.exports}var xt;function sn(){return xt||(xt=1,function(s){const r=ue(),a=en(),n=tn(),e=nn(),t=on(),i=Bt(),u=le(),l=rn();function h(T){return unescape(encodeURIComponent(T)).length}function v(T,I,S){const M=[];let $;for(;($=T.exec(S))!==null;)M.push({data:$[0],index:$.index,mode:I,length:$[0].length});return M}function C(T){const I=v(i.NUMERIC,r.NUMERIC,T),S=v(i.ALPHANUMERIC,r.ALPHANUMERIC,T);let M,$;return u.isKanjiModeEnabled()?(M=v(i.BYTE,r.BYTE,T),$=v(i.KANJI,r.KANJI,T)):(M=v(i.BYTE_KANJI,r.BYTE,T),$=[]),I.concat(S,M,$).sort(function(d,f){return d.index-f.index}).map(function(d){return{data:d.data,mode:d.mode,length:d.length}})}function w(T,I){switch(I){case r.NUMERIC:return a.getBitsLength(T);case r.ALPHANUMERIC:return n.getBitsLength(T);case r.KANJI:return t.getBitsLength(T);case r.BYTE:return e.getBitsLength(T)}}function m(T){return T.reduce(function(I,S){const M=I.length-1>=0?I[I.length-1]:null;return M&&M.mode===S.mode?(I[I.length-1].data+=S.data,I):(I.push(S),I)},[])}function E(T){const I=[];for(let S=0;S<T.length;S++){const M=T[S];switch(M.mode){case r.NUMERIC:I.push([M,{data:M.data,mode:r.ALPHANUMERIC,length:M.length},{data:M.data,mode:r.BYTE,length:M.length}]);break;case r.ALPHANUMERIC:I.push([M,{data:M.data,mode:r.BYTE,length:M.length}]);break;case r.KANJI:I.push([M,{data:M.data,mode:r.BYTE,length:h(M.data)}]);break;case r.BYTE:I.push([{data:M.data,mode:r.BYTE,length:h(M.data)}])}}return I}function B(T,I){const S={},M={start:{}};let $=["start"];for(let g=0;g<T.length;g++){const d=T[g],f=[];for(let p=0;p<d.length;p++){const A=d[p],b=""+g+p;f.push(b),S[b]={node:A,lastCount:0},M[b]={};for(let _=0;_<$.length;_++){const k=$[_];S[k]&&S[k].node.mode===A.mode?(M[k][b]=w(S[k].lastCount+A.length,A.mode)-w(S[k].lastCount,A.mode),S[k].lastCount+=A.length):(S[k]&&(S[k].lastCount=A.length),M[k][b]=w(A.length,A.mode)+4+r.getCharCountIndicator(A.mode,I))}}$=f}for(let g=0;g<$.length;g++)M[$[g]].end=0;return{map:M,table:S}}function z(T,I){let S;const M=r.getBestModeForData(T);if(S=r.from(I,M),S!==r.BYTE&&S.bit<M.bit)throw new Error('"'+T+'" cannot be encoded with mode '+r.toString(S)+`.
 Suggested mode is: `+r.toString(M));switch(S===r.KANJI&&!u.isKanjiModeEnabled()&&(S=r.BYTE),S){case r.NUMERIC:return new a(T);case r.ALPHANUMERIC:return new n(T);case r.KANJI:return new t(T);case r.BYTE:return new e(T)}}s.fromArray=function(I){return I.reduce(function(S,M){return typeof M=="string"?S.push(z(M,null)):M.data&&S.push(z(M.data,M.mode)),S},[])},s.fromString=function(I,S){const M=C(I,u.isKanjiModeEnabled()),$=E(M),g=B($,S),d=l.find_path(g.map,"start","end"),f=[];for(let p=1;p<d.length-1;p++)f.push(g.table[d[p]].node);return s.fromArray(m(f))},s.rawSplit=function(I){return s.fromArray(C(I,u.isKanjiModeEnabled()))}}(Le)),Le}var Ct;function an(){if(Ct)return ke;Ct=1;const s=le(),r=Ye(),a=Ht(),n=Ot(),e=Kt(),t=Yt(),i=Jt(),u=St(),l=Wt(),h=Xt(),v=Zt(),C=ue(),w=sn();function m(g,d){const f=g.size,p=t.getPositions(d);for(let A=0;A<p.length;A++){const b=p[A][0],_=p[A][1];for(let k=-1;k<=7;k++)if(!(b+k<=-1||f<=b+k))for(let R=-1;R<=7;R++)_+R<=-1||f<=_+R||(k>=0&&k<=6&&(R===0||R===6)||R>=0&&R<=6&&(k===0||k===6)||k>=2&&k<=4&&R>=2&&R<=4?g.set(b+k,_+R,!0,!0):g.set(b+k,_+R,!1,!0))}}function E(g){const d=g.size;for(let f=8;f<d-8;f++){const p=f%2===0;g.set(f,6,p,!0),g.set(6,f,p,!0)}}function B(g,d){const f=e.getPositions(d);for(let p=0;p<f.length;p++){const A=f[p][0],b=f[p][1];for(let _=-2;_<=2;_++)for(let k=-2;k<=2;k++)_===-2||_===2||k===-2||k===2||_===0&&k===0?g.set(A+_,b+k,!0,!0):g.set(A+_,b+k,!1,!0)}}function z(g,d){const f=g.size,p=h.getEncodedBits(d);let A,b,_;for(let k=0;k<18;k++)A=Math.floor(k/3),b=k%3+f-8-3,_=(p>>k&1)===1,g.set(A,b,_,!0),g.set(b,A,_,!0)}function T(g,d,f){const p=g.size,A=v.getEncodedBits(d,f);let b,_;for(b=0;b<15;b++)_=(A>>b&1)===1,b<6?g.set(b,8,_,!0):b<8?g.set(b+1,8,_,!0):g.set(p-15+b,8,_,!0),b<8?g.set(8,p-b-1,_,!0):b<9?g.set(8,15-b-1+1,_,!0):g.set(8,15-b-1,_,!0);g.set(p-8,8,1,!0)}function I(g,d){const f=g.size;let p=-1,A=f-1,b=7,_=0;for(let k=f-1;k>0;k-=2)for(k===6&&k--;;){for(let R=0;R<2;R++)if(!g.isReserved(A,k-R)){let X=!1;_<d.length&&(X=(d[_]>>>b&1)===1),g.set(A,k-R,X),b--,b===-1&&(_++,b=7)}if(A+=p,A<0||f<=A){A-=p,p=-p;break}}}function S(g,d,f){const p=new a;f.forEach(function(R){p.put(R.mode.bit,4),p.put(R.getLength(),C.getCharCountIndicator(R.mode,g)),R.write(p)});const A=s.getSymbolTotalCodewords(g),b=u.getTotalCodewordsCount(g,d),_=(A-b)*8;for(p.getLengthInBits()+4<=_&&p.put(0,4);p.getLengthInBits()%8!==0;)p.putBit(0);const k=(_-p.getLengthInBits())/8;for(let R=0;R<k;R++)p.put(R%2?17:236,8);return M(p,g,d)}function M(g,d,f){const p=s.getSymbolTotalCodewords(d),A=u.getTotalCodewordsCount(d,f),b=p-A,_=u.getBlocksCount(d,f),k=p%_,R=_-k,X=Math.floor(p/_),Z=Math.floor(b/_),be=Z+1,pe=X-Z,ve=new l(pe);let fe=0;const x=new Array(_),c=new Array(_);let q=0;const N=new Uint8Array(g.buffer);for(let K=0;K<_;K++){const de=K<R?Z:be;x[K]=N.slice(fe,fe+de),c[K]=ve.encode(x[K]),fe+=de,q=Math.max(q,de)}const y=new Uint8Array(p);let O=0,j,V;for(j=0;j<q;j++)for(V=0;V<_;V++)j<x[V].length&&(y[O++]=x[V][j]);for(j=0;j<pe;j++)for(V=0;V<_;V++)y[O++]=c[V][j];return y}function $(g,d,f,p){let A;if(Array.isArray(g))A=w.fromArray(g);else if(typeof g=="string"){let X=d;if(!X){const Z=w.rawSplit(g);X=h.getBestVersionForData(Z,f)}A=w.fromString(g,X||40)}else throw new Error("Invalid data");const b=h.getBestVersionForData(A,f);if(!b)throw new Error("The amount of data is too big to be stored in a QR Code");if(!d)d=b;else if(d<b)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+b+`.
`);const _=S(d,f,A),k=s.getSymbolSize(d),R=new n(k);return m(R,d),E(R),B(R,d),T(R,f,0),d>=7&&z(R,d),I(R,_),isNaN(p)&&(p=i.getBestMask(R,T.bind(null,R,f))),i.applyMask(p,R),T(R,f,p),{modules:R,version:d,errorCorrectionLevel:f,maskPattern:p,segments:A}}return ke.create=function(d,f){if(typeof d>"u"||d==="")throw new Error("No input text");let p=r.M,A,b;return typeof f<"u"&&(p=r.from(f.errorCorrectionLevel,r.M),A=h.from(f.version),b=i.from(f.maskPattern),f.toSJISFunc&&s.setToSJISFunction(f.toSJISFunc)),$(d,A,p,b)},ke}var Ve={},He={},_t;function Rt(){return _t||(_t=1,function(s){function r(a){if(typeof a=="number"&&(a=a.toString()),typeof a!="string")throw new Error("Color should be defined as hex string");let n=a.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+a);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(t){return[t,t]}))),n.length===6&&n.push("F","F");const e=parseInt(n.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+n.slice(0,6).join("")}}s.getOptions=function(n){n||(n={}),n.color||(n.color={});const e=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,t=n.width&&n.width>=21?n.width:void 0,i=n.scale||4;return{width:t,scale:t?4:i,margin:e,color:{dark:r(n.color.dark||"#000000ff"),light:r(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},s.getScale=function(n,e){return e.width&&e.width>=n+e.margin*2?e.width/(n+e.margin*2):e.scale},s.getImageWidth=function(n,e){const t=s.getScale(n,e);return Math.floor((n+e.margin*2)*t)},s.qrToImageData=function(n,e,t){const i=e.modules.size,u=e.modules.data,l=s.getScale(i,t),h=Math.floor((i+t.margin*2)*l),v=t.margin*l,C=[t.color.light,t.color.dark];for(let w=0;w<h;w++)for(let m=0;m<h;m++){let E=(w*h+m)*4,B=t.color.light;if(w>=v&&m>=v&&w<h-v&&m<h-v){const z=Math.floor((w-v)/l),T=Math.floor((m-v)/l);B=C[u[z*i+T]?1:0]}n[E++]=B.r,n[E++]=B.g,n[E++]=B.b,n[E]=B.a}}}(He)),He}var kt;function ln(){return kt||(kt=1,function(s){const r=Rt();function a(e,t,i){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=i,t.width=i,t.style.height=i+"px",t.style.width=i+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}s.render=function(t,i,u){let l=u,h=i;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),i||(h=n()),l=r.getOptions(l);const v=r.getImageWidth(t.modules.size,l),C=h.getContext("2d"),w=C.createImageData(v,v);return r.qrToImageData(w.data,t,l),a(C,h,v),C.putImageData(w,0,0),h},s.renderToDataURL=function(t,i,u){let l=u;typeof l>"u"&&(!i||!i.getContext)&&(l=i,i=void 0),l||(l={});const h=s.render(t,i,l),v=l.type||"image/png",C=l.rendererOpts||{};return h.toDataURL(v,C.quality)}}(Ve)),Ve}var Oe={},Et;function un(){if(Et)return Oe;Et=1;const s=Rt();function r(e,t){const i=e.a/255,u=t+'="'+e.hex+'"';return i<1?u+" "+t+'-opacity="'+i.toFixed(2).slice(1)+'"':u}function a(e,t,i){let u=e+t;return typeof i<"u"&&(u+=" "+i),u}function n(e,t,i){let u="",l=0,h=!1,v=0;for(let C=0;C<e.length;C++){const w=Math.floor(C%t),m=Math.floor(C/t);!w&&!h&&(h=!0),e[C]?(v++,C>0&&w>0&&e[C-1]||(u+=h?a("M",w+i,.5+m+i):a("m",l,0),l=0,h=!1),w+1<t&&e[C+1]||(u+=a("h",v),v=0)):l++}return u}return Oe.render=function(t,i,u){const l=s.getOptions(i),h=t.modules.size,v=t.modules.data,C=h+l.margin*2,w=l.color.light.a?"<path "+r(l.color.light,"fill")+' d="M0 0h'+C+"v"+C+'H0z"/>':"",m="<path "+r(l.color.dark,"stroke")+' d="'+n(v,h,l.margin)+'"/>',E='viewBox="0 0 '+C+" "+C+'"',z='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+E+' shape-rendering="crispEdges">'+w+m+`</svg>
`;return typeof u=="function"&&u(null,z),z},Oe}var Tt;function dn(){if(Tt)return ce;Tt=1;const s=Vt(),r=an(),a=ln(),n=un();function e(t,i,u,l,h){const v=[].slice.call(arguments,1),C=v.length,w=typeof v[C-1]=="function";if(!w&&!s())throw new Error("Callback required as last argument");if(w){if(C<2)throw new Error("Too few arguments provided");C===2?(h=u,u=i,i=l=void 0):C===3&&(i.getContext&&typeof h>"u"?(h=l,l=void 0):(h=l,l=u,u=i,i=void 0))}else{if(C<1)throw new Error("Too few arguments provided");return C===1?(u=i,i=l=void 0):C===2&&!i.getContext&&(l=u,u=i,i=void 0),new Promise(function(m,E){try{const B=r.create(u,l);m(t(B,i,l))}catch(B){E(B)}})}try{const m=r.create(u,l);h(null,t(m,i,l))}catch(m){h(m)}}return ce.create=r.create,ce.toCanvas=e.bind(null,a.render),ce.toDataURL=e.bind(null,a.renderToDataURL),ce.toString=e.bind(null,function(t,i,u){return n.render(t,u)}),ce}var cn=dn();const fn=Dt(cn),gn={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},hn={key:0,class:"text-center py-8"},mn={key:1,class:"space-y-6"},pn={class:"flex space-x-1 bg-gray-100 rounded-lg p-1"},vn=["onClick"],yn={key:0,class:"space-y-4"},wn={class:"bg-green-50 border border-green-200 rounded-lg p-4"},bn={class:"flex items-center space-x-2"},xn=["value"],Cn=["disabled"],_n={class:"flex items-center space-x-2"},kn=["value"],En=["disabled"],Tn={class:"text-center"},Mn={class:"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg"},Sn={key:1,class:"space-y-4"},An=["disabled"],Bn={key:0,class:"flex items-center justify-center"},Rn={key:1},In={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md"},Dn={key:1,class:"p-3 bg-red-100 border border-red-400 text-red-700 rounded-md"},Pn={key:2,class:"space-y-4"},$n={class:"grid grid-cols-2 gap-4"},Nn={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-center"},Ln={class:"text-2xl font-bold text-blue-600"},qn={class:"bg-green-50 border border-green-200 rounded-lg p-4 text-center"},jn={class:"text-2xl font-bold text-green-600"},Un={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center"},Fn={class:"text-2xl font-bold text-yellow-600"},zn={key:0},Vn={class:"space-y-3"},Hn={class:"flex items-center justify-between mb-2"},On={class:"flex-1"},Kn={class:"text-sm font-medium text-gray-900"},Yn={class:"text-xs text-gray-500"},Jn={class:"flex items-center justify-between text-xs"},Qn={class:"text-gray-500"},Gn={key:0,class:"text-green-600"},Wn={key:1,class:"text-center py-8"},Xn={__name:"ReferralModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(s,{emit:r}){const a=s,n=r,e=L(!1),t=L("share"),i=L({}),u=L({}),l=L(""),h=L(!1),v=L(!1),C=L(""),w=L(null),m=[{id:"share",name:"Share"},{id:"invite",name:"Invite"},{id:"stats",name:"Stats"}],E=()=>{n("close")},B=async()=>{var g;e.value=!0;try{console.log("Loading referral data..."),i.value={referral_code:"DEMO123",referral_url:"https://medroid.ai/register?ref=DEMO123"},u.value={total_count:5,completed_count:3,pending_count:2,total_earnings:15,referrals:[{id:1,email:"<EMAIL>",status:"completed",created_at:new Date().toISOString(),earnings:3},{id:2,email:"<EMAIL>",status:"pending",created_at:new Date().toISOString(),earnings:0}]};try{const d=(g=document.head.querySelector('meta[name="csrf-token"]'))==null?void 0:g.getAttribute("content");d&&(Q.defaults.headers.common["X-CSRF-TOKEN"]=d);const f=await Q.get("/api/referrals/code",{headers:{Accept:"application/json","Content-Type":"application/json"},withCredentials:!0});console.log("Referral code response:",f.data),f.data&&(i.value=f.data);const p=await Q.get("/api/referrals/my",{headers:{Accept:"application/json","Content-Type":"application/json"},withCredentials:!0});console.log("Referral stats response:",p.data),p.data&&(u.value=p.data)}catch(d){console.log("API call failed, using demo data:",d.message)}await ne(),i.value.referral_url&&await z()}catch(d){console.error("Error in loadReferralData:",d)}finally{e.value=!1}},z=async()=>{if(!(!w.value||!i.value.referral_url))try{const g=w.value;g.width=128,g.height=128,await fn.toCanvas(g,i.value.referral_url,{width:128,margin:1,color:{dark:"#000000",light:"#FFFFFF"}})}catch(g){console.error("Error generating QR code:",g);const f=w.value.getContext("2d");f.fillStyle="#f3f4f6",f.fillRect(0,0,128,128),f.fillStyle="#6b7280",f.font="12px Arial",f.textAlign="center",f.fillText("QR Code",64,70)}},T=async g=>{if(g)try{await navigator.clipboard.writeText(g);const d=event.target.closest("button"),f=d.innerHTML;d.innerHTML='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',setTimeout(()=>{d.innerHTML=f},1e3)}catch(d){console.error("Failed to copy:",d);const f=document.createElement("textarea");f.value=g,document.body.appendChild(f),f.select();try{document.execCommand("copy")}catch(p){console.error("Fallback copy failed:",p)}document.body.removeChild(f)}},I=()=>{const g=`Join me on Medroid, your AI Doctor! Use my referral code: ${i.value.referral_code} or click: ${i.value.referral_url}`,d=`https://wa.me/?text=${encodeURIComponent(g)}`;window.open(d,"_blank")},S=()=>{const g="Join Medroid - Your AI Doctor",d=`Hi!

I'd like to invite you to join Medroid, an amazing AI-powered healthcare platform.

Use my referral code: ${i.value.referral_code}
Or click this link: ${i.value.referral_url}

Best regards!`,f=`mailto:?subject=${encodeURIComponent(g)}&body=${encodeURIComponent(d)}`;window.location.href=f},M=async()=>{var g,d;if(l.value){h.value=!0,v.value=!1,C.value="";try{await Q.post("/api/referrals/invite",{email:l.value}),v.value=!0,l.value="";const f=await Q.get("/api/referrals/my");u.value=f.data}catch(f){C.value=((d=(g=f.response)==null?void 0:g.data)==null?void 0:d.message)||"Failed to send invitation"}finally{h.value=!1}}},$=g=>{if(!g)return"";try{return new Date(g).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return g}};return Ge(()=>a.isOpen,g=>{g&&B()}),Ge(t,()=>{v.value=!1,C.value=""}),(g,d)=>s.isOpen?(P(),D("div",gn,[o("div",{class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity",onClick:ye(E,["self"])}),o("div",{class:"flex min-h-full items-center justify-center p-4",onClick:ye(E,["self"])},[o("div",{class:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",onClick:d[3]||(d[3]=ye(()=>{},["stop"]))},[o("div",{class:"flex items-center justify-between mb-6"},[d[5]||(d[5]=We('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"><svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg></div><div><h3 class="text-lg font-semibold text-gray-900">Refer &amp; Earn</h3><p class="text-sm text-gray-500">Earn $3 for each friend you refer</p></div></div>',1)),o("button",{onClick:E,class:"text-gray-400 hover:text-gray-600"},d[4]||(d[4]=[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e.value?(P(),D("div",hn,d[6]||(d[6]=[o("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"},null,-1),o("p",{class:"text-gray-600"},"Loading your referral details...",-1)]))):(P(),D("div",mn,[o("div",pn,[(P(),D(ie,null,ae(m,f=>o("button",{key:f.id,onClick:p=>t.value=f.id,class:oe(["flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors",t.value===f.id?"bg-white text-green-600 shadow-sm":"text-gray-600 hover:text-gray-900"])},F(f.name),11,vn)),64))]),t.value==="share"?(P(),D("div",yn,[o("div",wn,[d[8]||(d[8]=o("label",{class:"block text-sm font-medium text-green-800 mb-2"},"Your Referral Code",-1)),o("div",bn,[o("input",{value:i.value.referral_code||"Loading...",readonly:"",class:oe(["flex-1 px-3 py-2 bg-white border border-green-300 rounded-md text-lg font-mono text-center",{"text-gray-400":!i.value.referral_code}])},null,10,xn),o("button",{onClick:d[0]||(d[0]=f=>T(i.value.referral_code)),disabled:!i.value.referral_code,class:"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy code"},d[7]||(d[7]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,Cn)])]),o("div",null,[d[10]||(d[10]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Referral Link",-1)),o("div",_n,[o("input",{value:i.value.referral_url||"Loading...",readonly:"",class:oe(["flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm",{"text-gray-400":!i.value.referral_url}])},null,10,kn),o("button",{onClick:d[1]||(d[1]=f=>T(i.value.referral_url)),disabled:!i.value.referral_url,class:"px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy link"},d[9]||(d[9]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,En)])]),o("div",Tn,[d[11]||(d[11]=o("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"QR Code",-1)),o("div",Mn,[o("canvas",{ref_key:"qrCanvas",ref:w,class:"w-32 h-32"},null,512)]),d[12]||(d[12]=o("p",{class:"text-xs text-gray-500 mt-2"},"Share this QR code with friends",-1))]),o("div",{class:"grid grid-cols-2 gap-3"},[o("button",{onClick:I,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"},d[13]||(d[13]=[o("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},[o("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})],-1),o("span",{class:"text-sm font-medium"},"WhatsApp",-1)])),o("button",{onClick:S,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"},d[14]||(d[14]=[o("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1),o("span",{class:"text-sm font-medium"},"Email",-1)]))])])):W("",!0),t.value==="invite"?(P(),D("div",Sn,[o("form",{onSubmit:ye(M,["prevent"]),class:"space-y-4"},[o("div",null,[d[15]||(d[15]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Friend's Email",-1)),Ke(o("input",{"onUpdate:modelValue":d[2]||(d[2]=f=>l.value=f),type:"email",required:"",placeholder:"Enter your friend's email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"},null,512),[[Pt,l.value]])]),o("button",{type:"submit",disabled:h.value||!l.value,class:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[h.value?(P(),D("span",Bn,d[16]||(d[16]=[o("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),Mt(" Sending... ")]))):(P(),D("span",Rn,"Send Invitation"))],8,An)],32),v.value?(P(),D("div",In," Invitation sent successfully! ")):W("",!0),C.value?(P(),D("div",Dn,F(C.value),1)):W("",!0)])):W("",!0),t.value==="stats"?(P(),D("div",Pn,[o("div",$n,[o("div",Nn,[o("div",Ln,F(u.value.total_count||0),1),d[17]||(d[17]=o("div",{class:"text-sm text-blue-800"},"Total Referrals",-1))]),o("div",qn,[o("div",jn,F(u.value.completed_count||0),1),d[18]||(d[18]=o("div",{class:"text-sm text-green-800"},"Completed",-1))])]),o("div",Un,[o("div",Fn,"$"+F(u.value.completed_count*3||0),1),d[19]||(d[19]=o("div",{class:"text-sm text-yellow-800"},"Total Earnings",-1))]),u.value.referrals&&u.value.referrals.length>0?(P(),D("div",zn,[d[20]||(d[20]=o("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Recent Referrals",-1)),o("div",Vn,[(P(!0),D(ie,null,ae(u.value.referrals.slice(0,5),f=>(P(),D("div",{key:f.id,class:"p-3 bg-gray-50 rounded-lg border"},[o("div",Hn,[o("div",On,[o("div",Kn,F(f.email||"Anonymous Referral"),1),o("div",Yn,F($(f.created_at)),1)]),o("span",{class:oe(["text-xs px-2 py-1 rounded-full font-medium",f.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"])},F(f.status==="completed"?"Completed":"Pending"),3)]),o("div",Jn,[o("span",Qn," Credit: $"+F(f.credit_amount||3),1),f.status==="completed"&&f.completed_at?(P(),D("span",Gn," Completed "+F($(f.completed_at)),1)):W("",!0)])]))),128))])])):(P(),D("div",Wn,d[21]||(d[21]=[We('<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg></div><h4 class="text-sm font-medium text-gray-900 mb-1">No referrals yet</h4><p class="text-xs text-gray-500">Start sharing your referral code to earn rewards!</p>',3)])))])):W("",!0)]))])])])):W("",!0)}},Zn={class:"h-full flex flex-col bg-gray-50"},eo={key:0,class:"flex-1 flex flex-col items-center justify-center px-6 py-8"},to={key:1,class:"flex-1 flex flex-col items-center justify-center px-6 py-8"},no={class:"text-center mb-12"},oo={class:"mx-auto mb-6"},ro={class:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-12 max-w-2xl"},so=["onClick"],io=["innerHTML"],ao={class:"text-sm font-medium text-center"},lo={key:2,class:"flex-1 overflow-hidden"},uo={class:"h-full flex flex-col"},co={class:"max-w-4xl mx-auto px-4"},fo={key:0,class:"flex items-start space-x-2 max-w-[85%]"},go={class:"flex-1"},ho=["innerHTML"],mo={class:"text-xs text-gray-400 mt-0.5"},po={key:1,class:"flex justify-end max-w-[85%] ml-auto"},vo={class:"bg-teal-500 text-white px-3 py-2 rounded-2xl rounded-br-md shadow-sm"},yo={class:"text-sm"},wo={class:"text-xs text-teal-100 mt-0.5 text-right"},bo={key:0,class:"max-w-4xl mx-auto px-4"},xo={key:1,class:"max-w-4xl mx-auto px-4"},Co={class:"flex items-start space-x-2"},_o={class:"flex-1"},ko={class:"bg-white border border-gray-200 rounded-xl p-4 shadow-lg max-h-[500px] flex flex-col"},Eo={class:"mb-3"},To={class:"flex items-center justify-between mb-2"},Mo={class:"text-right"},So={class:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full"},Ao={key:0,class:"text-xs text-green-600 font-medium mt-1"},Bo={class:"flex space-x-1 mb-3 bg-gray-100 p-1 rounded-lg"},Ro={class:"flex space-x-2 mb-3"},Io={class:"flex-1"},Do=["value"],Po={class:"flex-1"},$o=["value"],No={key:0,class:"flex items-center justify-center py-8"},Lo={key:1,class:"text-center py-8"},qo={key:2,class:"flex-1 overflow-y-auto"},jo={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 pb-2"},Uo=["onClick"],Fo={class:"text-lg font-bold text-gray-900 group-hover:text-blue-700 mb-1"},zo={class:"text-sm text-gray-600 truncate mb-1"},Vo={class:"text-sm font-semibold text-green-600"},Ho={key:0,class:"text-xs text-gray-500 mt-1 truncate"},Oo={class:"mt-4 pt-3 border-t border-gray-200"},Ko={class:"flex items-center justify-between mb-2"},Yo={class:"flex items-center justify-between"},Jo={class:"text-xs text-gray-500"},Qo={class:"text-xs text-gray-400 mt-1"},Go={class:"border-t border-gray-200 bg-white px-6 py-4"},Wo={class:"max-w-4xl mx-auto"},Xo={__name:"Chat",setup(s){const r=L([]),a=L(""),n=L(!1),e=L(null),t=L(null),i=L(null),u=L(!1),l=L([]),h=L([]),v=L(!1),C=L(!1),w=L(!1),m=L(""),E=L("all"),B=L("earliest"),z=L([]),T=L([]),I=L(!1),S=[{title:"Chat",href:"/chat"}],M=[{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>`,text:"I have a headache",color:"bg-blue-50 text-blue-700 border-blue-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
    </svg>`,text:"Help me sleep better",color:"bg-green-50 text-green-700 border-green-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
    </svg>`,text:"Weight loss tips",color:"bg-purple-50 text-purple-700 border-purple-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>`,text:"Skin rash concerns",color:"bg-orange-50 text-orange-700 border-orange-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h6m-6 0l-.5 8.5A2 2 0 0013.5 21h-3A2 2 0 018.5 15.5L8 7z" />
    </svg>`,text:"Book appointment",color:"bg-teal-50 text-teal-700 border-teal-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>`,text:"Mental health tips",color:"bg-indigo-50 text-indigo-700 border-indigo-200"}],$=x=>{if(!x)return x;let c=x;return c=c.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),c=c.replace(/### (.*?):/g,"<h3>$1:</h3>"),c=c.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g,'<div class="mb-2"><strong>$1. $2</strong></div>'),c=c.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs,'<div class="ml-4 text-gray-600 mb-2">Why I think so: $1</div>'),c=c.replace(/^- \*\*(.*?)\*\*/gm,'<div class="mb-2"><strong>• $1</strong></div>'),c=c.replace(/^- (.*$)/gm,'<div class="mb-1 ml-4">• $1</div>'),c=c.replace(/\*\*(.*?):\*\*/g,'<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>'),c=c.replace(/\n\n/g,"<br><br>"),c=c.replace(/\n/g,"<br>"),c=c.replace(/<br><br><br>/g,"<br><br>"),c},g=async()=>{var x;console.log("TEST: loadAppointmentSlots called"),v.value=!0,r.value.push({id:Date.now(),type:"ai",content:"Loading available appointment slots for testing...",timestamp:new Date}),await ne(),b();try{console.log("TEST: Loading providers...");const q=(await Q.get("/api/providers/public")).data.providers||[];if(console.log("TEST: Providers loaded:",q.length),q.length===0){r.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.",timestamp:new Date});return}const N=new Date,y=[];let O=[];try{O=(await Q.get("/api/appointments/my")).data.appointments||[],console.log("TEST: Existing appointments:",O.length)}catch{console.log("TEST: Could not load existing appointments, proceeding without conflict check")}const j=[];for(let U=0;U<=14;U++){const H=new Date(N);H.setDate(H.getDate()+U);const G=H.toISOString().split("T")[0];j.push({value:G,label:H.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}),fullDate:H})}z.value=j,m.value||(m.value=""),T.value=[{value:"all",label:"All Providers"},...q.map(U=>{var H;return{value:U.id.toString(),label:`Dr. ${((H=U.user)==null?void 0:H.name)||"Provider"}`}})];for(let U=0;U<=14;U++){const H=new Date(N);H.setDate(H.getDate()+U);const G=H.toISOString().split("T")[0];console.log(`TEST: Checking date ${G}`);for(const Y of q){console.log(`TEST: Checking provider ${Y.id} - ${(x=Y.user)==null?void 0:x.name}`);try{const xe=(await Q.get(`/api/providers/${Y.id}/available-slots?date=${G}`)).data.available_slots||[];if(console.log(`TEST: Provider ${Y.id} has ${xe.length} slots on ${G}`),xe.length>0){const Ce=xe.filter(te=>{const re=`${G} ${te.start_time}`;return!O.some(ge=>{var Qe;return ge.status==="cancelled"?!1:`${ge.date||((Qe=ge.scheduled_at)==null?void 0:Qe.split("T")[0])} ${ge.time||ge.start_time}`===re})});if(console.log(`TEST: Provider ${Y.id} has ${Ce.length} available slots after filtering`),Ce.length===0){console.log(`TEST: All slots for provider ${Y.id} on ${G} are already booked`);continue}let J=null;try{const re=(await Q.get(`/api/providers/${Y.id}/services`)).data.services||[];console.log(`TEST: Services for provider ${Y.id}:`,re),re.length>0&&(J=re[0],console.log("TEST: Selected service:",J))}catch(te){console.error(`Error loading services for provider ${Y.id}:`,te)}const It=Ce.map(te=>{var re;return{id:`${Y.id}-${G}-${te.start_time}`,provider_id:Y.id,service_id:(J==null?void 0:J.id)||1,provider:`Dr. ${((re=Y.user)==null?void 0:re.name)||"Provider"}`,specialty:Y.specialization||"General Practice",date:H.toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"}),time:te.start_time,end_time:te.end_time,full_date:G,datetime:new Date(`${G}T${te.start_time}`),price:(J==null?void 0:J.price)||0,duration:(J==null?void 0:J.duration)||15,service_name:(J==null?void 0:J.name)||"Consultation"}});y.push(...It)}}catch(Je){console.error(`Error loading slots for provider ${Y.id} on ${G}:`,Je)}}if(y.length>=50)break}if(console.log("TEST: Total slots found:",y.length),y.length===0){r.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.",timestamp:new Date});return}y.sort((U,H)=>U.datetime-H.datetime);const V={};y.forEach(U=>{V[U.provider_id]||(V[U.provider_id]=[]),V[U.provider_id].push(U)});const K=[],de=Math.ceil(8/Object.keys(V).length);Object.keys(V).forEach(U=>{const H=V[U].slice(0,de);K.push(...H)}),K.sort((U,H)=>U.datetime-H.datetime),h.value=K,C.value=!1,u.value=!0,Z(),await ne(),b()}catch(c){console.error("Error loading appointment slots:",c),r.value.push({id:Date.now(),type:"ai",content:"I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.",timestamp:new Date})}finally{v.value=!1,await ne(),b()}},d=async x=>{try{const c={provider_id:x.provider_id,service_id:x.service_id||1,date:x.full_date,time_slot:{start_time:x.time,end_time:x.end_time},reason:"Appointment booked through AI chat",notes:`Booked via Medroid AI chat on ${new Date().toLocaleDateString()}`,currency:"GBP"},q=await Q.post("/api/appointments/with-payment",c);if(q.data.appointment){const N=q.data.appointment,y=q.data.payment;if(y&&y.client_secret){const O={id:Date.now(),type:"ai",content:`🏥 **Appointment Created - Payment Required**

**Provider:** ${x.provider}
**Date:** ${x.date}
**Time:** ${x.time}
**Appointment ID:** #${N.id}
**Amount:** £${y.amount}

Your appointment has been created and is pending payment. You'll be redirected to complete the payment process in a moment.

Please complete the payment to confirm your appointment.`,timestamp:new Date,formatted:$(`🏥 **Appointment Created - Payment Required**

**Provider:** ${x.provider}
**Date:** ${x.date}
**Time:** ${x.time}
**Appointment ID:** #${N.id}
**Amount:** £${y.amount}

Your appointment has been created and is pending payment. You'll be redirected to complete the payment process in a moment.

Please complete the payment to confirm your appointment.`)};if(r.value.push(O),i.value)try{await Q.post("/api/chat/message",{conversation_id:i.value,message:O.content,role:"assistant"})}catch(j){console.error("Error saving payment message to chat history:",j)}setTimeout(()=>{window.location.href=`/appointments/${N.id}/payment`},3e3)}else{const O={id:Date.now(),type:"ai",content:`✅ **Appointment Confirmed!**

**Provider:** ${x.provider}
**Date:** ${x.date}
**Time:** ${x.time}
**Appointment ID:** #${N.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`,timestamp:new Date,formatted:$(`✅ **Appointment Confirmed!**

**Provider:** ${x.provider}
**Date:** ${x.date}
**Time:** ${x.time}
**Appointment ID:** #${N.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`)};if(r.value.push(O),i.value)try{await Q.post("/api/chat/message",{conversation_id:i.value,message:O.content,role:"assistant"})}catch(j){console.error("Error saving confirmation message to chat history:",j)}}}else throw new Error("No appointment data returned");u.value=!1,await ne(),b()}catch(c){console.error("Error booking appointment:",c);const q={id:Date.now(),type:"ai",content:`I apologize, but there was an error booking your appointment with ${x.provider}. This could be due to:

• The time slot may no longer be available
• A technical issue with our booking system

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,timestamp:new Date,formatted:$(`I apologize, but there was an error booking your appointment with ${x.provider}. This could be due to:

• The time slot may no longer be available
• A technical issue with our booking system

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)};r.value.push(q),u.value=!1,await ne(),b()}},f=async(x=null)=>{var q,N;const c=x||a.value.trim();if(c){r.value.push({id:Date.now(),type:"user",content:c,timestamp:new Date}),x||(a.value=""),await ne(),b(),n.value=!0;try{if(!i.value){const V=await fetch("/api/chat/start",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((q=document.querySelector('meta[name="csrf-token"]'))==null?void 0:q.getAttribute("content"))||""},credentials:"include",body:JSON.stringify({})}),K=await V.json();if(!V.ok)throw console.error("Start conversation failed:",K),new Error(K.message||"Failed to start conversation");if(!K.conversation_id)throw new Error("No conversation ID returned");i.value=String(K.conversation_id)}const y={conversation_id:String(i.value),message:c,include_patient_context:!0,generate_title:!0,request_full_response:!1};console.log("Sending payload:",y);const O=await fetch("/api/chat/message",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((N=document.querySelector('meta[name="csrf-token"]'))==null?void 0:N.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(y)}),j=await O.json();if(O.ok&&j.message){const V={id:Date.now()+1,type:"ai",content:j.message,timestamp:new Date,formatted:$(j.message)};r.value.push(V);const K=j.message.toLowerCase(),U=["appointment","book","schedule","available slots","see a doctor","consultation","visit","meet with","appointment options provided","would you like to schedule"].some(H=>K.includes(H));(j.appointment_options||j.show_appointments||U)&&setTimeout(()=>{g()},1e3)}else console.error("Failed to get AI response:",j),r.value.push({id:Date.now()+1,type:"ai",content:`Sorry, I encountered an error: ${j.message||"Please try again."}`,timestamp:new Date})}catch(y){console.error("An error occurred while sending your message:",y),r.value.push({id:Date.now()+1,type:"ai",content:`Sorry, I encountered an error: ${y.message||"Please try again."}`,timestamp:new Date})}finally{n.value=!1,await ne(),b(),t.value&&t.value.focus&&setTimeout(()=>{t.value.focus()},100)}}},p=x=>{f(x.text)},A=x=>{x.key==="Enter"&&!x.shiftKey&&(x.preventDefault(),f())},b=()=>{e.value&&(e.value.scrollTop=e.value.scrollHeight)},_=x=>new Date(x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),k=()=>{r.value=[],i.value=null,u.value=!1,a.value="",t.value&&t.value.focus&&setTimeout(()=>{t.value.focus()},100)},R=()=>{I.value=!0},X=()=>{I.value=!1},Z=()=>{let x=[...h.value];m.value&&m.value!==""&&m.value!=="all"&&(x=x.filter(c=>c.full_date===m.value)),E.value&&E.value!=="all"&&(x=x.filter(c=>c.provider_id.toString()===E.value)),l.value=x,C.value=!0},be=()=>{Z()},pe=()=>{Z()},ve=x=>{B.value=x,Z()},fe=async x=>{if(x){w.value=!0;try{const q=(await Q.get(`/api/chat/conversation/${x}`)).data;q&&q.id&&(i.value=x,q.messages&&Array.isArray(q.messages)&&(r.value=q.messages.map(N=>({id:N._id||N.id||Date.now()+Math.random(),type:N.role==="user"?"user":"ai",content:N.content||N.message,timestamp:new Date(N.timestamp||N.created_at||Date.now()),formatted:N.role!=="user"?$(N.content||N.message):void 0})),await ne(),b()))}catch(c){console.error("Error loading conversation:",c),i.value=null,r.value=[]}finally{w.value=!1}}};return $t(()=>{const c=new URLSearchParams(window.location.search).get("conversation");c&&fe(c)}),(x,c)=>(P(),D(ie,null,[he(Nt(Lt),{title:"Chat - Medroid"}),he(jt,{breadcrumbs:S},{default:qt(()=>{var q,N;return[o("div",Zn,[o("div",{class:"bg-white border-b border-gray-200 px-6 py-4"},[o("div",{class:"max-w-4xl mx-auto flex items-center justify-between"},[c[9]||(c[9]=o("div",{class:"flex items-center space-x-3"},[o("div",{class:"w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center"},[o("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[o("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})])]),o("div",null,[o("h1",{class:"text-lg font-semibold text-gray-900"},"Medroid AI"),o("p",{class:"text-sm text-gray-500"},"Your AI Doctor")])],-1)),o("div",{class:"flex items-center space-x-3"},[o("button",{onClick:k,class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"},c[6]||(c[6]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),o("span",null,"New Chat",-1)])),o("button",{onClick:g,class:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 shadow-md hover:shadow-lg"},c[7]||(c[7]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),o("span",null,"TEST Slots",-1)])),o("button",{onClick:R,class:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors duration-200"},c[8]||(c[8]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),o("span",null,"Refer & Earn",-1)]))])])]),w.value?(P(),D("div",eo,c[10]||(c[10]=[o("div",{class:"text-center"},[o("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),o("p",{class:"text-gray-600"},"Loading conversation...")],-1)]))):r.value.length===0?(P(),D("div",to,[o("div",no,[o("div",oo,[he(Ft,{size:80,"show-shadow":!0})]),c[11]||(c[11]=o("h1",{class:"text-4xl font-bold text-gray-900 mb-4"},"Welcome to Medroid",-1)),c[12]||(c[12]=o("p",{class:"text-lg text-gray-600 max-w-md mx-auto"}," Your personal AI Doctor. Ask me anything about your health, symptoms, medications, or wellness advice. ",-1))]),o("div",ro,[(P(),D(ie,null,ae(M,y=>o("button",{key:y.text,onClick:O=>p(y),class:oe([y.color,"flex flex-col items-center p-4 rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105"])},[o("div",{class:"mb-2",innerHTML:y.icon},null,8,io),o("span",ao,F(y.text),1)],10,so)),64))])])):(P(),D("div",lo,[o("div",uo,[o("div",{ref_key:"chatContainer",ref:e,class:"flex-1 overflow-y-auto py-2 space-y-2"},[o("div",co,[(P(!0),D(ie,null,ae(r.value,y=>(P(),D("div",{key:y.id,class:oe(["flex",y.type==="user"?"justify-end":"justify-start"])},[y.type==="ai"?(P(),D("div",fo,[c[13]||(c[13]=o("div",{class:"w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1"},[o("svg",{class:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[o("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})])],-1)),o("div",go,[o("div",{class:"text-gray-800 leading-snug prose prose-sm max-w-none text-sm",innerHTML:y.formatted||y.content},null,8,ho),o("div",mo,F(_(y.timestamp)),1)])])):(P(),D("div",po,[o("div",vo,[o("div",yo,F(y.content),1),o("div",wo,F(_(y.timestamp)),1)])]))],2))),128))]),n.value?(P(),D("div",bo,c[14]||(c[14]=[o("div",{class:"flex items-start space-x-2"},[o("div",{class:"w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1"},[o("svg",{class:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[o("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})])]),o("div",{class:"flex-1"},[o("div",{class:"flex space-x-1"},[o("div",{class:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"}),o("div",{class:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.1s"}}),o("div",{class:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce",style:{"animation-delay":"0.2s"}})])])],-1)]))):W("",!0),u.value?(P(),D("div",xo,[o("div",Co,[c[24]||(c[24]=o("div",{class:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1 shadow-lg"},[o("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",_o,[o("div",ko,[o("div",Eo,[o("div",To,[c[15]||(c[15]=o("h3",{class:"text-base font-semibold text-gray-900 flex items-center"},[o("span",{class:"mr-2"},"📅"),Mt(" Available Appointments ")],-1)),o("div",Mo,[o("span",So,F(l.value.length)+" slots found ",1),l.value.length>0?(P(),D("div",Ao," Earliest: "+F((q=l.value[0])==null?void 0:q.date)+" at "+F((N=l.value[0])==null?void 0:N.time),1)):W("",!0)])]),o("div",Bo,[o("button",{onClick:c[0]||(c[0]=y=>ve("earliest")),class:oe(["flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",B.value==="earliest"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🚀 Earliest Available ",2),o("button",{onClick:c[1]||(c[1]=y=>ve("all")),class:oe(["flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",B.value==="all"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 📋 All Appointments ",2)]),o("div",Ro,[o("div",Io,[c[17]||(c[17]=o("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"Date",-1)),Ke(o("select",{"onUpdate:modelValue":c[2]||(c[2]=y=>m.value=y),onChange:be,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[c[16]||(c[16]=o("option",{value:""},"All Dates",-1)),(P(!0),D(ie,null,ae(z.value,y=>(P(),D("option",{key:y.value,value:y.value},F(y.label),9,Do))),128))],544),[[Xe,m.value]])]),o("div",Po,[c[18]||(c[18]=o("label",{class:"block text-xs font-medium text-gray-700 mb-1"},"Provider",-1)),Ke(o("select",{"onUpdate:modelValue":c[3]||(c[3]=y=>E.value=y),onChange:pe,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[(P(!0),D(ie,null,ae(T.value,y=>(P(),D("option",{key:y.value,value:y.value},F(y.label),9,$o))),128))],544),[[Xe,E.value]])])])]),v.value?(P(),D("div",No,c[19]||(c[19]=[o("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"},null,-1),o("span",{class:"ml-3 text-sm text-gray-600"},"Finding available slots...",-1)]))):l.value.length===0?(P(),D("div",Lo,c[20]||(c[20]=[o("div",{class:"text-gray-400 mb-2"},[o("svg",{class:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1),o("p",{class:"text-sm text-gray-500 mb-2"},"No appointments found for your selection",-1),o("p",{class:"text-xs text-gray-400"},"Try selecting a different date or provider",-1)]))):(P(),D("div",qo,[o("div",jo,[(P(!0),D(ie,null,ae(l.value,y=>(P(),D("button",{key:y.id,onClick:O=>d(y),class:"group relative bg-white border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:shadow-md transition-all duration-200 text-center min-h-[80px] flex flex-col justify-center"},[o("div",{class:oe(["absolute top-1 right-1 w-2 h-2 rounded-full",y.provider_id===3?"bg-green-500":"bg-blue-500"])},null,2),o("div",Fo,F(y.time),1),o("div",zo,F(y.provider.replace("Dr. ","")),1),o("div",Vo," £"+F(y.price),1),m.value===""?(P(),D("div",Ho,F(y.date.split(",")[0]),1)):W("",!0),c[21]||(c[21]=o("div",{class:"absolute inset-0 bg-blue-50 opacity-0 group-hover:opacity-50 rounded-lg transition-opacity duration-200"},null,-1))],8,Uo))),128))])])),o("div",Oo,[o("div",Ko,[c[22]||(c[22]=o("p",{class:"text-xs text-gray-500"}," Click on any slot to book your appointment ",-1)),o("button",{onClick:c[4]||(c[4]=y=>u.value=!1),class:"text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"}," Cancel ")]),o("div",Yo,[c[23]||(c[23]=o("div",{class:"flex items-center space-x-4 text-xs text-gray-500"},[o("div",{class:"flex items-center space-x-1"},[o("div",{class:"w-2 h-2 bg-green-500 rounded-full"}),o("span",null,"Dr. Raja Mohan")]),o("div",{class:"flex items-center space-x-1"},[o("div",{class:"w-2 h-2 bg-blue-500 rounded-full"}),o("span",null,"Dr. Sarah Johnson")])],-1)),o("div",Jo," Showing "+F(l.value.length)+" available slots ",1)])])]),o("div",Qo,F(_(new Date)),1)])])])):W("",!0)],512)])])),o("div",Go,[o("div",Wo,[he(Ut,{ref_key:"chatInputRef",ref:t,modelValue:a.value,"onUpdate:modelValue":c[5]||(c[5]=y=>a.value=y),placeholder:"Type your health question...","is-loading":n.value,"show-tools":!0,"show-version":!0,onSend:f,onKeydown:A},null,8,["modelValue","is-loading"])])])]),he(Xn,{"is-open":I.value,onClose:X},null,8,["is-open"])]}),_:1})],64))}},lr=zt(Xo,[["__scopeId","data-v-6334d727"]]);export{lr as default};
