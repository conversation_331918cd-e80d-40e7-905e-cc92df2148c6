import{d as l,R as f,v as i,f as d,l as t,j as a,i as r,u as s,q as c,x as u,m,g as _}from"./vendor-bzEMSiaZ.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";import{_ as C}from"./index-Oq2yf1P1.js";import{_ as g,a as V}from"./Label.vue_vue_type_script_setup_true_lang-B-E--Xd1.js";import{L as b,_ as x}from"./AuthLayout.vue_vue_type_script_setup_true_lang-CXnZV5tQ.js";import"./Primitive-CNCx3Yln.js";import"./index-CLsNIGVi.js";import"./createLucideIcon-BJGbtoZV.js";const h={class:"space-y-6"},v={class:"grid gap-2"},y={class:"flex items-center"},M=l({__name:"ConfirmPassword",setup($){const o=f({password:""}),n=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return(k,e)=>(d(),i(x,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(c),{title:"Confirm password"}),r("form",{onSubmit:u(n,["prevent"])},[r("div",h,[r("div",v,[a(s(g),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[m("Password")])),_:1}),a(s(V),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=p=>s(o).password=p),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(w,{message:s(o).errors.password},null,8,["message"])]),r("div",y,[a(s(C),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(d(),i(s(b),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),e[2]||(e[2]=m(" Confirm Password "))]),_:1},8,["disabled"])])])],32)]),_:1}))}});export{M as default};
