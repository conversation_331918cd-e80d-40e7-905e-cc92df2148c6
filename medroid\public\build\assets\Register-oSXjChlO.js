import{d as U,R as q,r as p,o as A,e as d,f as n,j as l,i as t,u as r,q as E,I as V,g as m,t as g,x as R,y as i,z as y,O as _,Q as B,m as M,l as T,P as D,F as I,s as N,K as O,p as j}from"./vendor-bzEMSiaZ.js";import{_ as c}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const G={class:"min-h-screen bg-medroid-sage flex"},$={class:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-medroid-cream"},K={class:"max-w-md w-full"},Q={key:0,class:"mb-4 text-center text-sm font-medium text-medroid-teal"},W={key:0},Y={key:1},J={class:"flex space-x-6"},X={class:"flex items-center"},Z={class:"flex items-center"},ee={class:"flex items-center"},te={key:2},oe=["placeholder"],re={class:"relative"},se=["type","autocomplete","placeholder"],ae={key:0,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},de={key:1,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ne={key:3},le={key:4},ie={class:"relative"},ue=["type"],me={key:0,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ce={key:1,class:"w-5 h-5 text-medroid-slate",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},pe={class:"pt-4"},ge=["disabled"],ve={class:"text-center pt-4"},fe={class:"text-sm text-medroid-slate"},be={key:5,class:"text-center"},xe={class:"hidden lg:flex lg:w-1/2 bg-medroid-sage flex-col"},he={class:"flex-1 p-6 overflow-y-auto space-y-4 chat-container"},ye=U({__name:"Register",props:{canResetPassword:{type:Boolean,default:!1},status:{type:String,default:null},isLoginMode:{type:Boolean,default:!1}},setup(v){const z=v,o=q({name:"",email:"",password:"",password_confirmation:"",role:"patient",gender:"",date_of_birth:"",referral_code:""}),f=p(!1),b=p(!1),x=p(!1),a=p(z.isLoginMode),w=p([]),k=[{type:"user",text:"I have been feeling tired lately and having headaches. What could be the cause?"},{type:"bot",text:"I understand your concern. Fatigue and headaches can have various causes. Can you tell me more about when these symptoms started and if you've noticed any patterns?"},{type:"user",text:"It started about a week ago, mostly in the afternoons."},{type:"bot",text:"Based on your symptoms, this could be related to dehydration, stress, or sleep patterns. I recommend drinking more water, ensuring adequate sleep, and monitoring your symptoms. If they persist, please consult with a healthcare provider."},{type:"user",text:"Thank you! Should I be concerned about anything specific?"},{type:"bot",text:"Monitor for severe headaches, fever, or vision changes. These would require immediate medical attention. For now, focus on hydration and rest. Feel better soon!"}],C=()=>{w.value=[];let u=0;const e=()=>{var s;if(u<k.length){w.value.push(k[u]),u++;const h=((s=k[u-1])==null?void 0:s.type)==="bot"?2e3:1500;setTimeout(e,h)}else setTimeout(()=>{C()},3e3)};setTimeout(e,1e3)},S=()=>{x.value=!0;const u=a.value?"login":"register";o.post(route(u),{onFinish:()=>{a.value?o.reset("password"):o.reset("password","password_confirmation"),x.value=!1}})},F=()=>{f.value=!f.value},L=()=>{b.value=!b.value},P=()=>{a.value=!a.value,o.reset()};return A(()=>{C()}),(u,e)=>(n(),d(I,null,[l(r(E),{title:a.value?"Sign In":"Register"},null,8,["title"]),t("div",G,[t("div",$,[t("div",K,[e[22]||(e[22]=V('<div class="text-left mb-8" data-v-c84eaec3><h1 class="text-4xl font-bold text-medroid-navy mb-2" data-v-c84eaec3> Your health, </h1><h2 class="text-4xl font-bold text-medroid-navy mb-4" data-v-c84eaec3> our priority </h2><p class="text-medroid-slate mb-8" data-v-c84eaec3> AI-powered healthcare that puts your wellness first. </p></div><button type="button" class="w-full flex items-center justify-center px-4 py-3 border border-medroid-border rounded-lg shadow-sm text-sm font-medium text-medroid-navy bg-white hover:bg-medroid-sage transition-colors duration-200 mb-6" data-v-c84eaec3><svg class="w-5 h-5 mr-3" viewBox="0 0 24 24" data-v-c84eaec3><path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" data-v-c84eaec3></path><path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" data-v-c84eaec3></path><path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" data-v-c84eaec3></path><path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" data-v-c84eaec3></path></svg> Continue with Google </button><div class="relative mb-6" data-v-c84eaec3><div class="absolute inset-0 flex items-center" data-v-c84eaec3><div class="w-full border-t border-medroid-border" data-v-c84eaec3></div></div><div class="relative flex justify-center text-sm" data-v-c84eaec3><span class="px-2 bg-medroid-cream text-medroid-slate" data-v-c84eaec3>OR</span></div></div>',3)),v.status?(n(),d("div",Q,g(v.status),1)):m("",!0),t("form",{class:"space-y-5",onSubmit:R(S,["prevent"])},[a.value?m("",!0):(n(),d("div",W,[i(t("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=s=>r(o).name=s),name:"name",type:"text",autocomplete:"name",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:"Enter your full name"},null,512),[[y,r(o).name]]),l(c,{class:"mt-2",message:r(o).errors.name},null,8,["message"])])),a.value?m("",!0):(n(),d("div",Y,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-medroid-navy mb-3"},"Gender (Optional)",-1)),t("div",J,[t("label",X,[i(t("input",{type:"radio","onUpdate:modelValue":e[1]||(e[1]=s=>r(o).gender=s),value:"male",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[_,r(o).gender]]),e[9]||(e[9]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Male",-1))]),t("label",Z,[i(t("input",{type:"radio","onUpdate:modelValue":e[2]||(e[2]=s=>r(o).gender=s),value:"female",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[_,r(o).gender]]),e[10]||(e[10]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Female",-1))]),t("label",ee,[i(t("input",{type:"radio","onUpdate:modelValue":e[3]||(e[3]=s=>r(o).gender=s),value:"other",name:"gender",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[_,r(o).gender]]),e[11]||(e[11]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Other",-1))])]),l(c,{class:"mt-2",message:r(o).errors.gender},null,8,["message"])])),a.value?m("",!0):(n(),d("div",te,[e[13]||(e[13]=t("label",{for:"date_of_birth",class:"block text-sm font-medium text-medroid-navy mb-2"},"Date of Birth (Optional)",-1)),i(t("input",{id:"date_of_birth","onUpdate:modelValue":e[4]||(e[4]=s=>r(o).date_of_birth=s),name:"date_of_birth",type:"date",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy bg-white"},null,512),[[y,r(o).date_of_birth]]),l(c,{class:"mt-2",message:r(o).errors.date_of_birth},null,8,["message"]),e[14]||(e[14]=t("p",{class:"mt-1 text-xs text-medroid-slate"},"Helps us provide better healthcare recommendations",-1))])),t("div",null,[i(t("input",{id:"email","onUpdate:modelValue":e[5]||(e[5]=s=>r(o).email=s),name:"email",type:"email",autocomplete:"email",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:a.value?"Enter your personal or work email":"Enter your email address"},null,8,oe),[[y,r(o).email]]),l(c,{class:"mt-2",message:r(o).errors.email},null,8,["message"])]),t("div",null,[t("div",re,[i(t("input",{id:"password","onUpdate:modelValue":e[6]||(e[6]=s=>r(o).password=s),name:"password",type:f.value?"text":"password",autocomplete:a.value?"current-password":"new-password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:a.value?"Enter your password":"Create a strong password"},null,8,se),[[B,r(o).password]]),t("button",{type:"button",onClick:F,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[f.value?(n(),d("svg",ae,e[15]||(e[15]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),d("svg",de,e[16]||(e[16]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),l(c,{class:"mt-2",message:r(o).errors.password},null,8,["message"])]),a.value?m("",!0):(n(),d("div",ne,[e[17]||(e[17]=t("label",{for:"referral_code",class:"block text-sm font-medium text-medroid-navy mb-2"},"Referral Code (Optional)",-1)),i(t("input",{id:"referral_code","onUpdate:modelValue":e[7]||(e[7]=s=>r(o).referral_code=s),name:"referral_code",type:"text",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:"Enter referral code"},null,512),[[y,r(o).referral_code]]),l(c,{class:"mt-2",message:r(o).errors.referral_code},null,8,["message"]),e[18]||(e[18]=t("p",{class:"mt-1 text-xs text-medroid-slate"},"Have a referral code? Enter it here to get bonus points!",-1))])),a.value?m("",!0):(n(),d("div",le,[t("div",ie,[i(t("input",{id:"password_confirmation","onUpdate:modelValue":e[8]||(e[8]=s=>r(o).password_confirmation=s),name:"password_confirmation",type:b.value?"text":"password",autocomplete:"new-password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white",placeholder:"Confirm your password"},null,8,ue),[[B,r(o).password_confirmation]]),t("button",{type:"button",onClick:L,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[b.value?(n(),d("svg",me,e[19]||(e[19]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)]))):(n(),d("svg",ce,e[20]||(e[20]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)])))])]),l(c,{class:"mt-2",message:r(o).errors.password_confirmation},null,8,["message"])])),t("div",pe,[t("button",{type:"submit",disabled:r(o).processing||x.value,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"},g(r(o).processing||x.value?a.value?"Signing In...":"Creating Account...":a.value?"Sign In":"Sign Up"),9,ge)]),t("div",ve,[t("p",fe,[M(g(a.value?"Don't have an account?":"Already have an account?")+" ",1),t("button",{type:"button",onClick:P,class:"font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline"},g(a.value?"Sign up":"Sign in here"),1)])]),a.value&&v.canResetPassword?(n(),d("div",be,[l(r(D),{href:u.route("password.request"),class:"text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200 underline"},{default:T(()=>e[21]||(e[21]=[M(" Forgot password? ")])),_:1},8,["href"])])):m("",!0)],32),e[23]||(e[23]=t("div",{class:"text-center mt-6"},[t("p",{class:"text-xs text-medroid-slate"},[M(" By continuing, you acknowledge Medroid's "),t("a",{href:"#",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Privacy Policy")])],-1))])]),t("div",xe,[e[24]||(e[24]=t("div",{class:"bg-white p-6 border-b border-medroid-border"},[t("h3",{class:"text-xl font-semibold text-medroid-navy mb-2"},"Experience Medroid"),t("p",{class:"text-medroid-slate text-sm"},"Chat with our AI doctor for instant health insights")],-1)),t("div",he,[(n(!0),d(I,null,N(w.value,(s,h)=>(n(),d("div",{key:h,class:j([s.type==="user"?"flex justify-end":"flex justify-start","animate-fade-in-up"]),style:O({animationDelay:`${h*.1}s`})},[t("div",{class:j(["message-bubble max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm",s.type==="user"?"bg-medroid-orange text-white rounded-br-md shadow-lg":"bg-white text-medroid-navy border border-medroid-border rounded-bl-md shadow-sm hover:shadow-md"])},g(s.text),3)],6))),128))]),e[25]||(e[25]=V('<div class="p-6 bg-white border-t border-medroid-border" data-v-c84eaec3><div class="flex items-center space-x-3" data-v-c84eaec3><input type="text" placeholder="Type your health question here..." class="flex-1 px-4 py-3 border border-medroid-border rounded-full focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange text-sm text-medroid-navy placeholder-medroid-slate" disabled data-v-c84eaec3><button class="bg-medroid-orange hover:bg-medroid-orange/90 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50" disabled data-v-c84eaec3><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c84eaec3><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" data-v-c84eaec3></path></svg></button></div><div class="mt-4 text-center" data-v-c84eaec3><a href="#" class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200" data-v-c84eaec3> Learn more about Medroid → </a></div></div>',1))])])],64))}}),Me=H(ye,[["__scopeId","data-v-c84eaec3"]]);export{Me as default};
