import{r as g,e as i,f as u,j as k,u as j,q as B,l as q,i as e,g as x,m,t as n,x as A,y as c,z as y,F as w,s as C,A as P,a as F,W as h}from"./vendor-bzEMSiaZ.js";import{_ as L}from"./AppLayout.vue_vue_type_script_setup_true_lang-CIylfZ-g.js";import"./MedroidLogo-c6q-kzU_.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-CNCx3Yln.js";import"./createLucideIcon-BJGbtoZV.js";const I={class:"max-w-2xl mx-auto py-8 px-4"},T={class:"bg-white rounded-lg shadow-md p-6 mb-6"},U={class:"border-l-4 border-blue-500 pl-4 mb-6"},H={class:"space-y-2 text-sm text-gray-600"},W={key:0},$={class:"bg-gray-50 rounded-lg p-4"},O={class:"flex justify-between items-center"},R={class:"text-2xl font-bold text-green-600"},E={class:"bg-white rounded-lg shadow-md p-6"},J={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4 mb-4"},G={class:"flex"},K={class:"ml-3"},Q={class:"text-sm text-red-800"},X={key:1,class:"bg-green-50 border border-green-200 rounded-md p-4 mb-4"},Z={class:"flex"},ee={class:"ml-3"},te={class:"text-sm text-green-800"},se={class:"grid grid-cols-3 gap-4"},ae=["value"],re=["value"],ne={class:"flex space-x-4 pt-6"},oe=["disabled"],le={key:0,class:"flex items-center justify-center"},de={key:1},fe={__name:"AppointmentPayment",props:{appointment:Object,stripe_public_key:String},setup(o){const _=o,v=g(!1),l=g(""),f=g(""),s=g({card_number:"",exp_month:"",exp_year:"",cvc:"",cardholder_name:""}),M=[{name:"Appointments",href:"/appointments"},{name:"Payment",href:"#"}],V=r=>new Date(r).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),D=r=>typeof r=="object"&&r.start_time?`${r.start_time} - ${r.end_time}`:r,z=async()=>{var r,t,a,p;if(S()){v.value=!0,l.value="",f.value="";try{const d={payment_intent_id:_.appointment.payment_intent_id,appointment_id:_.appointment.id,card_number:s.value.card_number.replace(/\s/g,""),exp_month:parseInt(s.value.exp_month),exp_year:parseInt(s.value.exp_year),cvc:s.value.cvc,cardholder_name:s.value.cardholder_name},b=await F.post("/api/payments/process-web-payment",d);b.data.success?(f.value="Payment processed successfully! Redirecting...",setTimeout(()=>{h.visit("/appointments",{onSuccess:()=>{h.reload({data:{message:"Payment completed successfully!"}})}})},2e3)):l.value=b.data.message||"Payment failed. Please try again."}catch(d){if(console.error("Payment error:",d),(t=(r=d.response)==null?void 0:r.data)!=null&&t.message)l.value=d.response.data.message;else if((p=(a=d.response)==null?void 0:a.data)!=null&&p.errors){const b=Object.values(d.response.data.errors).flat();l.value=b.join(", ")}else l.value="An error occurred while processing your payment. Please try again."}finally{v.value=!1}}},S=()=>{if(!s.value.card_number||!s.value.exp_month||!s.value.exp_year||!s.value.cvc||!s.value.cardholder_name)return l.value="Please fill in all required fields.",!1;const r=s.value.card_number.replace(/\s/g,"");if(r.length<13||r.length>19)return l.value="Please enter a valid card number.",!1;const t=new Date().getFullYear(),a=new Date().getMonth()+1,p=parseInt(s.value.exp_year),d=parseInt(s.value.exp_month);return p<t||p===t&&d<a?(l.value="Card has expired. Please use a valid card.",!1):s.value.cvc.length<3||s.value.cvc.length>4?(l.value="Please enter a valid CVC.",!1):!0},Y=()=>{var a;let r=s.value.card_number.replace(/\s/g,"").replace(/[^0-9]/gi,"");const t=((a=r.match(/.{1,4}/g))==null?void 0:a.join(" "))||r;s.value.card_number=t},N=()=>{h.visit("/appointments")};return(r,t)=>(u(),i(w,null,[k(j(B),{title:"Payment - Medroid"}),k(L,{breadcrumbs:M},{default:q(()=>[e("div",I,[e("div",T,[t[14]||(t[14]=e("h1",{class:"text-2xl font-bold text-gray-900 mb-4"},"Complete Payment",-1)),e("div",U,[t[12]||(t[12]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-2"},"Appointment Details",-1)),e("div",H,[e("p",null,[t[5]||(t[5]=e("span",{class:"font-medium"},"Provider:",-1)),m(" "+n(o.appointment.provider.name),1)]),e("p",null,[t[6]||(t[6]=e("span",{class:"font-medium"},"Specialization:",-1)),m(" "+n(o.appointment.provider.specialization),1)]),e("p",null,[t[7]||(t[7]=e("span",{class:"font-medium"},"Service:",-1)),m(" "+n(o.appointment.service.name),1)]),e("p",null,[t[8]||(t[8]=e("span",{class:"font-medium"},"Date:",-1)),m(" "+n(V(o.appointment.date)),1)]),e("p",null,[t[9]||(t[9]=e("span",{class:"font-medium"},"Time:",-1)),m(" "+n(D(o.appointment.time_slot)),1)]),e("p",null,[t[10]||(t[10]=e("span",{class:"font-medium"},"Reason:",-1)),m(" "+n(o.appointment.reason),1)]),o.appointment.notes?(u(),i("p",W,[t[11]||(t[11]=e("span",{class:"font-medium"},"Notes:",-1)),m(" "+n(o.appointment.notes),1)])):x("",!0)])]),e("div",$,[e("div",O,[t[13]||(t[13]=e("span",{class:"text-lg font-semibold text-gray-900"},"Total Amount:",-1)),e("span",R,"£"+n(o.appointment.amount),1)])])]),e("div",E,[t[25]||(t[25]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Payment Information",-1)),l.value?(u(),i("div",J,[e("div",G,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",K,[e("p",Q,n(l.value),1)])])])):x("",!0),f.value?(u(),i("div",X,[e("div",Z,[t[16]||(t[16]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),e("div",ee,[e("p",te,n(f.value),1)])])])):x("",!0),e("form",{onSubmit:A(z,["prevent"]),class:"space-y-4"},[e("div",null,[t[17]||(t[17]=e("label",{for:"cardholder_name",class:"block text-sm font-medium text-gray-700 mb-1"}," Cardholder Name * ",-1)),c(e("input",{id:"cardholder_name","onUpdate:modelValue":t[0]||(t[0]=a=>s.value.cardholder_name=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"John Doe"},null,512),[[y,s.value.cardholder_name]])]),e("div",null,[t[18]||(t[18]=e("label",{for:"card_number",class:"block text-sm font-medium text-gray-700 mb-1"}," Card Number * ",-1)),c(e("input",{id:"card_number","onUpdate:modelValue":t[1]||(t[1]=a=>s.value.card_number=a),onInput:Y,type:"text",required:"",maxlength:"23",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"1234 5678 9012 3456"},null,544),[[y,s.value.card_number]])]),e("div",se,[e("div",null,[t[20]||(t[20]=e("label",{for:"exp_month",class:"block text-sm font-medium text-gray-700 mb-1"}," Month * ",-1)),c(e("select",{id:"exp_month","onUpdate:modelValue":t[2]||(t[2]=a=>s.value.exp_month=a),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[19]||(t[19]=e("option",{value:""},"MM",-1)),(u(),i(w,null,C(12,a=>e("option",{key:a,value:a.toString().padStart(2,"0")},n(a.toString().padStart(2,"0")),9,ae)),64))],512),[[P,s.value.exp_month]])]),e("div",null,[t[22]||(t[22]=e("label",{for:"exp_year",class:"block text-sm font-medium text-gray-700 mb-1"}," Year * ",-1)),c(e("select",{id:"exp_year","onUpdate:modelValue":t[3]||(t[3]=a=>s.value.exp_year=a),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[21]||(t[21]=e("option",{value:""},"YYYY",-1)),(u(),i(w,null,C(20,a=>e("option",{key:a,value:(new Date().getFullYear()+a-1).toString()},n(new Date().getFullYear()+a-1),9,re)),64))],512),[[P,s.value.exp_year]])]),e("div",null,[t[23]||(t[23]=e("label",{for:"cvc",class:"block text-sm font-medium text-gray-700 mb-1"}," CVC * ",-1)),c(e("input",{id:"cvc","onUpdate:modelValue":t[4]||(t[4]=a=>s.value.cvc=a),type:"text",required:"",maxlength:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"123"},null,512),[[y,s.value.cvc]])])]),e("div",ne,[e("button",{type:"button",onClick:N,class:"flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}," Cancel "),e("button",{type:"submit",disabled:v.value,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"},[v.value?(u(),i("span",le,t[24]||(t[24]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),m(" Processing... ")]))):(u(),i("span",de," Pay £"+n(o.appointment.amount),1))],8,oe)])],32),t[26]||(t[26]=e("div",{class:"mt-6 p-4 bg-gray-50 rounded-md"},[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-gray-600"}," Your payment information is secure and encrypted. We use Stripe for payment processing. ")])])],-1))])])]),_:1})],64))}};export{fe as default};
