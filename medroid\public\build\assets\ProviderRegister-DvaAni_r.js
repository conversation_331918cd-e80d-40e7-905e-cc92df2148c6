import{d as O,C as R,r as b,e as d,f as u,j as l,i as o,u as F,q as T,x as D,y as i,z as p,m,O as v,Q as E,A as $,F as x,s as U,g as I,t as y,a as Y}from"./vendor-bzEMSiaZ.js";import{_ as a}from"./InputError.vue_vue_type_script_setup_true_lang-DE-ncPZb.js";const G={class:"min-h-screen bg-medroid-sage py-12"},H={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},J={class:"bg-white rounded-lg shadow-lg p-8"},Q={class:"border-b border-gray-200 pb-6"},K={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},W={class:"flex space-x-4"},X={class:"flex items-center"},Z={class:"flex items-center"},ee={class:"flex items-center"},oe={class:"border-b border-gray-200 pb-6"},re={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se={class:"relative"},te=["type"],ne={key:0,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},le={key:1,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ie={class:"relative"},ae=["type"],de={key:0,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ue={key:1,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},me={class:"border-b border-gray-200 pb-6"},pe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ce=["value"],ge={class:"mt-6"},fe={class:"mt-6"},be={class:"grid grid-cols-2 md:grid-cols-3 gap-3"},ve=["value","checked","onChange"],xe={class:"pt-6"},ye=["disabled"],we={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Ve=O({__name:"ProviderRegister",props:{specializations:{type:Array,default:()=>[]},languages:{type:Array,default:()=>[]}},setup(w){const r=R({name:"",email:"",password:"",password_confirmation:"",phone:"",gender:"",specialization:"",license_number:"",years_of_experience:0,bio:"",languages:[]}),n=b({}),g=b(!1),f=b(!1),c=b(!1),B=async()=>{c.value=!0,n.value={};try{console.log("Submitting form data:",r);const t=await Y.post("/api/provider/register",r,{headers:{"Content-Type":"application/json",Accept:"application/json"}});console.log("Response:",t.data),t.data.success&&(alert("Registration submitted successfully! You will receive an email confirmation within 24 hours."),Object.keys(r).forEach(e=>{e==="languages"?r[e]=[]:e==="years_of_experience"?r[e]=0:r[e]=""}))}catch(t){if(console.error("Full error object:",t),t.response)if(console.error("Error response:",t.response.data),console.error("Error status:",t.response.status),t.response.status===422){n.value=t.response.data.errors||{},console.error("Validation errors:",n.value),console.error("Debug info:",t.response.data.debug);const e=Object.values(n.value).flat();e.length>0&&alert(`Validation errors:
`+e.join(`
`))}else alert(`Error ${t.response.status}: ${t.response.data.message||"An error occurred during registration."}`);else t.request?(console.error("No response received:",t.request),alert("No response from server. Please check your connection and try again.")):(console.error("Request setup error:",t.message),alert("An error occurred setting up the request. Please try again."))}finally{c.value=!1}},S=()=>{g.value=!g.value},N=()=>{f.value=!f.value},L=t=>{const e=r.languages.indexOf(t);e>-1?r.languages.splice(e,1):r.languages.push(t)};return(t,e)=>{var k,h,C,V,M,z,P,q,_,j,A;return u(),d(x,null,[l(F(T),{title:"Provider Registration"}),o("div",G,[o("div",H,[e[37]||(e[37]=o("div",{class:"text-center mb-8"},[o("h1",{class:"text-4xl font-bold text-medroid-navy mb-4"}," Join Our Provider Network "),o("p",{class:"text-lg text-medroid-slate max-w-2xl mx-auto"}," Become part of Medroid's healthcare network and help provide quality care to patients worldwide. ")],-1)),o("div",J,[o("form",{onSubmit:D(B,["prevent"]),class:"space-y-6"},[o("div",Q,[e[19]||(e[19]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Personal Information",-1)),o("div",K,[o("div",null,[e[12]||(e[12]=o("label",{for:"name",class:"block text-sm font-medium text-medroid-navy mb-2"},"Full Name *",-1)),i(o("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=s=>r.name=s),type:"text",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your full name"},null,512),[[p,r.name]]),l(a,{class:"mt-2",message:(k=n.value.name)==null?void 0:k[0]},null,8,["message"])]),o("div",null,[e[13]||(e[13]=o("label",{for:"email",class:"block text-sm font-medium text-medroid-navy mb-2"},"Email Address *",-1)),i(o("input",{id:"email","onUpdate:modelValue":e[1]||(e[1]=s=>r.email=s),type:"email",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your email address"},null,512),[[p,r.email]]),l(a,{class:"mt-2",message:(h=n.value.email)==null?void 0:h[0]},null,8,["message"])]),o("div",null,[e[14]||(e[14]=o("label",{for:"phone",class:"block text-sm font-medium text-medroid-navy mb-2"},"Phone Number *",-1)),i(o("input",{id:"phone","onUpdate:modelValue":e[2]||(e[2]=s=>r.phone=s),type:"tel",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your phone number"},null,512),[[p,r.phone]]),l(a,{class:"mt-2",message:(C=n.value.phone)==null?void 0:C[0]},null,8,["message"])]),o("div",null,[e[18]||(e[18]=o("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Gender *",-1)),o("div",W,[o("label",X,[i(o("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>r.gender=s),type:"radio",value:"male",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,r.gender]]),e[15]||(e[15]=m(" Male "))]),o("label",Z,[i(o("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>r.gender=s),type:"radio",value:"female",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,r.gender]]),e[16]||(e[16]=m(" Female "))]),o("label",ee,[i(o("input",{"onUpdate:modelValue":e[5]||(e[5]=s=>r.gender=s),type:"radio",value:"other",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,r.gender]]),e[17]||(e[17]=m(" Other "))])]),l(a,{class:"mt-2",message:(V=n.value.gender)==null?void 0:V[0]},null,8,["message"])])])]),o("div",oe,[e[26]||(e[26]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Account Security",-1)),o("div",re,[o("div",null,[e[22]||(e[22]=o("label",{for:"password",class:"block text-sm font-medium text-medroid-navy mb-2"},"Password *",-1)),o("div",se,[i(o("input",{id:"password","onUpdate:modelValue":e[6]||(e[6]=s=>r.password=s),type:g.value?"text":"password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Create a strong password"},null,8,te),[[E,r.password]]),o("button",{type:"button",onClick:S,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[g.value?(u(),d("svg",le,e[21]||(e[21]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(u(),d("svg",ne,e[20]||(e[20]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])]),l(a,{class:"mt-2",message:(M=n.value.password)==null?void 0:M[0]},null,8,["message"])]),o("div",null,[e[25]||(e[25]=o("label",{for:"password_confirmation",class:"block text-sm font-medium text-medroid-navy mb-2"},"Confirm Password *",-1)),o("div",ie,[i(o("input",{id:"password_confirmation","onUpdate:modelValue":e[7]||(e[7]=s=>r.password_confirmation=s),type:f.value?"text":"password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Confirm your password"},null,8,ae),[[E,r.password_confirmation]]),o("button",{type:"button",onClick:N,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[f.value?(u(),d("svg",ue,e[24]||(e[24]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(u(),d("svg",de,e[23]||(e[23]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])]),l(a,{class:"mt-2",message:(z=n.value.password_confirmation)==null?void 0:z[0]},null,8,["message"])])])]),o("div",me,[e[34]||(e[34]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Professional Information",-1)),o("div",pe,[o("div",null,[e[28]||(e[28]=o("label",{for:"specialization",class:"block text-sm font-medium text-medroid-navy mb-2"},"Specialization *",-1)),i(o("select",{id:"specialization","onUpdate:modelValue":e[8]||(e[8]=s=>r.specialization=s),required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"},[e[27]||(e[27]=o("option",{value:""},"Select your specialization",-1)),(u(!0),d(x,null,U(w.specializations,s=>(u(),d("option",{key:s,value:s},y(s),9,ce))),128))],512),[[$,r.specialization]]),l(a,{class:"mt-2",message:(P=n.value.specialization)==null?void 0:P[0]},null,8,["message"])]),o("div",null,[e[29]||(e[29]=o("label",{for:"license_number",class:"block text-sm font-medium text-medroid-navy mb-2"},"License Number *",-1)),i(o("input",{id:"license_number","onUpdate:modelValue":e[9]||(e[9]=s=>r.license_number=s),type:"text",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your medical license number"},null,512),[[p,r.license_number]]),l(a,{class:"mt-2",message:(q=n.value.license_number)==null?void 0:q[0]},null,8,["message"])]),o("div",null,[e[30]||(e[30]=o("label",{for:"years_of_experience",class:"block text-sm font-medium text-medroid-navy mb-2"},"Years of Experience *",-1)),i(o("input",{id:"years_of_experience","onUpdate:modelValue":e[10]||(e[10]=s=>r.years_of_experience=s),type:"number",min:"0",max:"50",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter years of experience"},null,512),[[p,r.years_of_experience]]),l(a,{class:"mt-2",message:(_=n.value.years_of_experience)==null?void 0:_[0]},null,8,["message"])])]),o("div",ge,[e[31]||(e[31]=o("label",{for:"bio",class:"block text-sm font-medium text-medroid-navy mb-2"},"Professional Bio *",-1)),i(o("textarea",{id:"bio","onUpdate:modelValue":e[11]||(e[11]=s=>r.bio=s),rows:"4",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Tell us about your professional background, experience, and approach to patient care (minimum 50 characters)"},null,512),[[p,r.bio]]),e[32]||(e[32]=o("p",{class:"mt-1 text-sm text-medroid-slate"},"Minimum 50 characters, maximum 1000 characters",-1)),l(a,{class:"mt-2",message:(j=n.value.bio)==null?void 0:j[0]},null,8,["message"])]),o("div",fe,[e[33]||(e[33]=o("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Languages Spoken",-1)),o("div",be,[(u(!0),d(x,null,U(w.languages,s=>(u(),d("label",{key:s,class:"flex items-center"},[o("input",{type:"checkbox",value:s,checked:r.languages.includes(s),onChange:ke=>L(s),class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,40,ve),m(" "+y(s),1)]))),128))]),l(a,{class:"mt-2",message:(A=n.value.languages)==null?void 0:A[0]},null,8,["message"])])]),o("div",xe,[o("button",{type:"submit",disabled:c.value,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 disabled:bg-medroid-orange/50 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center"},[c.value?(u(),d("svg",we,e[35]||(e[35]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):I("",!0),m(" "+y(c.value?"Submitting Application...":"Submit Application"),1)],8,ye)]),e[36]||(e[36]=o("div",{class:"text-center pt-4"},[o("p",{class:"text-sm text-medroid-slate"},[m(" By submitting this application, you agree to our "),o("a",{href:"#",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Terms of Service"),m(" and "),o("a",{href:"#",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Privacy Policy")])],-1))],32)])])])],64)}}});export{Ve as default};
