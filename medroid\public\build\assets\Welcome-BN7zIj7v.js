import{d as ee,r as o,J as te,o as E,H as ae,e as l,f as n,j as C,i as t,l as F,u as A,q as se,g as f,p as D,I as b,F as V,m as O,P as oe,s as le,y as _,A as ne,O as j,a as $,B as z,t as ie}from"./vendor-bzEMSiaZ.js";import{C as G}from"./ChatInput-Cgkfazpi.js";import{_ as re}from"./_plugin-vue_export-helper-DlAUqK2U.js";const de={class:"max-w-lg"},ue={class:"space-y-4"},ce={class:"text-sm text-gray-500"},ve={class:"hidden lg:flex lg:w-1/2 bg-gray-50 flex-col px-8 lg:px-16 justify-center"},me={class:"max-w-lg mx-auto w-full"},ge={key:0,class:"space-y-6 mb-10"},pe={key:1,class:"space-y-6 mb-6 overflow-y-auto chat-messages-container"},fe={key:0,class:"flex items-start space-x-3"},be={class:"flex-1"},xe=["innerHTML"],ye={key:1,class:"flex justify-end"},he={class:"bg-medroid-orange text-white px-4 py-3 rounded-2xl rounded-br-md shadow-lg max-w-xs lg:max-w-md text-sm"},we={key:0,class:"flex items-start space-x-3"},ke={key:2},Ie={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ce={class:"bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"},De={class:"space-y-4"},_e={class:"flex space-x-4"},Te={class:"flex items-center"},Me={class:"flex items-center"},Se={class:"flex items-center"},Ae={class:"flex space-x-3 mt-8"},Ve={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},je={class:"bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"},$e={key:2,class:"fixed bottom-0 left-0 right-0 lg:left-1/2 lg:right-0 bg-gray-50 z-20"},ze={class:"px-8 lg:px-16 py-6"},Be={class:"max-w-lg mx-auto w-full"},Le=ee({__name:"Welcome",setup(Pe){var U;const B=o([]),x=o(0),r=o(null),y=o(!1),K=(U=te().props.auth)==null?void 0:U.user,d=o(!1),m=o(!1),i=o(""),g=o(null),c=o(""),p=o([]),h=o(!1),w=o(!1),k=o(""),v=o(""),L=o(!1),T=o(0),u=o(null),P=[{id:1,type:"ai",content:"Hi! I'm your AI Doctor. How are you feeling today?",timestamp:new Date},{id:2,type:"user",content:"I've been having trouble sleeping lately. Any suggestions?",timestamp:new Date},{id:3,type:"ai",content:"Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.",timestamp:new Date},{id:4,type:"user",content:"That makes sense. I'll try that tonight. Thank you!",timestamp:new Date},{id:5,type:"ai",content:"You're welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?",timestamp:new Date},{id:6,type:"user",content:"Yes, that would be helpful. I think stress might be affecting my sleep.",timestamp:new Date},{id:7,type:"ai",content:"Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.",timestamp:new Date},{id:8,type:"user",content:"I'll definitely try that breathing technique. Thank you for the personalized advice!",timestamp:new Date}],M=()=>{B.value=[],x.value=0,W()},W=()=>{r.value&&clearInterval(r.value),r.value=setInterval(()=>{if(x.value<P.length){const s={...P[x.value]};s.timestamp=new Date,B.value.push(s),x.value++}else setTimeout(()=>{y.value&&M()},4e3),r.value&&(clearInterval(r.value),r.value=null)},1500)};E(()=>{y.value=!0,setTimeout(()=>{M()},1e3)});const R=()=>"anon_"+Math.random().toString(36).substr(2,9)+"_"+Date.now(),q=async()=>{var s;if(i.value.trim())try{m.value=!0,console.log("Starting anonymous chat..."),c.value||(c.value=R()),console.log("Anonymous ID:",c.value),console.log("Initial message:",i.value.trim());const e=await $.post("/api/anonymous/chat/start");console.log("Start API Response:",e.data),g.value=e.data.conversation_id,c.value=e.data.anonymous_id,p.value.push({id:Date.now().toString(),type:"user",content:i.value.trim(),timestamp:new Date});const a=i.value.trim();i.value="",await Y();const I=await $.post("/api/anonymous/chat/message",{conversation_id:String(g.value),anonymous_id:c.value,message:a,gender:v.value||null,age:k.value||null,request_full_response:!1,generate_title:!0});console.log("Message API Response:",I.data),I.data.message&&p.value.push({id:(Date.now()+1).toString(),type:"ai",content:I.data.message,timestamp:new Date}),T.value++,T.value===1&&!L.value&&setTimeout(()=>{h.value=!0},1e3),I.data.requires_auth&&setTimeout(()=>{w.value=!0},1e3),await z(),S(),setTimeout(()=>{u.value&&u.value.focus&&u.value.focus()},100)}catch(e){console.error("Error starting chat:",e),console.error("Error details:",(s=e.response)==null?void 0:s.data),alert("Failed to start chat. Please try again.")}finally{m.value=!1}},H=async()=>{var s;if(!(!i.value.trim()||!g.value))try{m.value=!0,console.log("Sending message..."),p.value.push({id:Date.now().toString(),type:"user",content:i.value.trim(),timestamp:new Date});const e=i.value.trim();i.value="",console.log("Message to send:",e),console.log("Conversation ID:",g.value);const a=await $.post("/api/anonymous/chat/message",{conversation_id:String(g.value),message:e,anonymous_id:c.value,gender:v.value||null,age:k.value||null,request_full_response:!1,generate_title:!0});console.log("Message API Response:",a.data),a.data.message&&p.value.push({id:(Date.now()+1).toString(),type:"ai",content:a.data.message,timestamp:new Date}),T.value++,a.data.requires_auth&&setTimeout(()=>{w.value=!0},1e3),await z(),S(),setTimeout(()=>{u.value&&u.value.focus&&u.value.focus()},100)}catch(e){console.error("Error sending message:",e),console.error("Error details:",(s=e.response)==null?void 0:s.data),alert("Failed to send message. Please try again.")}finally{m.value=!1}},Y=async()=>{d.value=!0,r.value&&(clearInterval(r.value),r.value=null),await z(),setTimeout(()=>{u.value&&u.value.focus&&u.value.focus()},300),S()},S=()=>{const s=document.querySelector(".chat-messages-container");s&&(s.scrollTop=s.scrollHeight)},J=()=>{L.value=!0,h.value=!1},Z=s=>{let e=s;return e=e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),e=e.replace(/^(\d+\.\s+)(.+)$/gm,'<div class="list-item"><span class="list-number">$1</span>$2</div>'),e=e.replace(/^-\s+(.+)$/gm,'<div class="bullet-item">$1</div>'),e=e.replace(/^###\s+(.+)$/gm,'<h3 class="section-header">$1</h3>'),e=e.replace(/\*\*([A-Z\s]+:)\*\*/g,'<h4 class="subsection-header">$1</h4>'),e=e.replace(/\n/g,"<br>"),e},Q=()=>{localStorage.setItem("anonymous_conversation",JSON.stringify({conversation_id:g.value,anonymous_id:c.value,messages:p.value})),window.location.href="/login"},X=()=>{localStorage.setItem("anonymous_conversation",JSON.stringify({conversation_id:g.value,anonymous_id:c.value,messages:p.value})),window.location.href="/register"},N=s=>{s.key==="Enter"&&!s.shiftKey&&(s.preventDefault(),d.value?H():q())};return E(()=>{y.value=!0,c.value=R(),setTimeout(()=>{M()},1e3)}),ae(()=>{y.value=!1,r.value&&clearInterval(r.value)}),(s,e)=>(n(),l(V,null,[C(A(se),{title:"Medroid AI Doctor"},{default:F(()=>e[10]||(e[10]=[t("link",{rel:"preconnect",href:"https://rsms.me/"},null,-1),t("link",{rel:"stylesheet",href:"https://rsms.me/inter/inter.css"},null,-1),t("link",{href:"https://fonts.googleapis.com/css2?family=Material+Icons",rel:"stylesheet"},null,-1)])),_:1}),t("div",{class:D(["min-h-screen bg-white font-sans",{"chat-mode":d.value}])},[t("header",{class:D(["flex justify-between items-center px-8 py-6",{"fixed top-0 left-0 right-0 z-10 bg-white border-b border-gray-200":d.value}])},e[11]||(e[11]=[b('<div class="flex items-center space-x-2" data-v-cc9b9133><div class="w-8 h-8 bg-medroid-teal rounded-lg flex items-center justify-center" data-v-cc9b9133><svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24" data-v-cc9b9133><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" data-v-cc9b9133></path></svg></div><span class="text-xl font-semibold text-gray-900" data-v-cc9b9133>Medroid</span></div><div class="flex items-center space-x-2" data-v-cc9b9133><div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center" data-v-cc9b9133><svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cc9b9133><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-cc9b9133></path></svg></div></div>',2)]),2),t("main",{class:D(["flex min-h-[calc(100vh-100px)]",{"pb-32 lg:pb-40":d.value}])},[t("div",{class:D(["w-full lg:w-1/2 bg-white flex flex-col px-8 lg:px-16",{"justify-center":!d.value,"justify-start pt-16":d.value}])},[t("div",de,[e[14]||(e[14]=b('<div class="mb-12" data-v-cc9b9133><h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6" data-v-cc9b9133> Your free<br data-v-cc9b9133> personal<br data-v-cc9b9133><span class="text-teal-400" data-v-cc9b9133>AI Doctor</span> awaits<br data-v-cc9b9133> you. </h1><p class="text-gray-600 text-lg leading-relaxed" data-v-cc9b9133> Fast, free, and private medical consultations powered by AI. </p></div><div class="mb-12 space-y-5" data-v-cc9b9133><div class="flex items-center space-x-4" data-v-cc9b9133><div class="w-1 h-7 bg-teal-400 rounded-full" data-v-cc9b9133></div><span class="text-gray-800 text-lg font-medium" data-v-cc9b9133>100% free and private</span></div><div class="flex items-center space-x-4" data-v-cc9b9133><div class="w-1 h-7 bg-orange-400 rounded-full" data-v-cc9b9133></div><span class="text-gray-800 text-lg font-medium" data-v-cc9b9133>Instant medical advice</span></div><div class="flex items-center space-x-4" data-v-cc9b9133><div class="w-1 h-7 bg-orange-400 rounded-full" data-v-cc9b9133></div><span class="text-gray-800 text-lg font-medium" data-v-cc9b9133>Book real doctor appointments</span></div></div>',2)),t("div",ue,[A(K)?(n(),l("button",{key:1,onClick:e[1]||(e[1]=a=>s.$inertia.visit(s.route("dashboard"))),class:"w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"}," Go to Dashboard ")):(n(),l(V,{key:0},[t("button",{onClick:e[0]||(e[0]=a=>s.$inertia.visit(s.route("login"))),class:"w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200"}," Sign In "),t("p",ce,[e[13]||(e[13]=O(" Don't have an account? ")),C(A(oe),{href:s.route("register"),class:"text-teal-400 hover:text-teal-500 font-medium ml-1"},{default:F(()=>e[12]||(e[12]=[O(" Sign up here ")])),_:1},8,["href"])])],64))])])],2),t("div",ve,[t("div",me,[e[18]||(e[18]=b('<div class="mb-8" data-v-cc9b9133><div class="flex items-start space-x-3 mb-8" data-v-cc9b9133><div class="w-8 h-8 bg-teal-400 rounded-full flex items-center justify-center flex-shrink-0" data-v-cc9b9133><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" data-v-cc9b9133><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" data-v-cc9b9133></path></svg></div><div class="flex-1" data-v-cc9b9133><p class="text-gray-800 font-medium" data-v-cc9b9133>Hi, I&#39;m Medroid, your personal AI doctor.</p></div></div></div>',1)),d.value?f("",!0):(n(),l("div",ge,e[15]||(e[15]=[t("div",{class:"bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100"},[t("p",{class:"text-gray-800 leading-relaxed"}," As an AI doctor, I'm fast and free. I've already conducted 1,000,000+ consultations! ")],-1),t("div",{class:"bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100"},[t("p",{class:"text-gray-800 leading-relaxed"}," When you're done, you can have a video consultation with a world class doctor, if you want, for just £55. ")],-1)]))),d.value?(n(),l("div",pe,[(n(!0),l(V,null,le(p.value,a=>(n(),l("div",{key:a.id,class:"animate-fade-in-up"},[a.type==="ai"?(n(),l("div",fe,[e[16]||(e[16]=t("div",{class:"w-8 h-8 bg-medroid-teal rounded-full flex items-center justify-center flex-shrink-0"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})])],-1)),t("div",be,[t("div",{class:"text-gray-800 leading-relaxed text-sm formatted-message",innerHTML:Z(a.content)},null,8,xe)])])):(n(),l("div",ye,[t("div",he,ie(a.content),1)]))]))),128)),m.value?(n(),l("div",we,e[17]||(e[17]=[b('<div class="w-8 h-8 bg-medroid-teal rounded-full flex items-center justify-center flex-shrink-0" data-v-cc9b9133><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" data-v-cc9b9133><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" data-v-cc9b9133></path></svg></div><div class="flex-1" data-v-cc9b9133><div class="flex space-x-1" data-v-cc9b9133><div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" data-v-cc9b9133></div><div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay:0.1s;" data-v-cc9b9133></div><div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay:0.2s;" data-v-cc9b9133></div></div></div>',2)]))):f("",!0)])):f("",!0),d.value?f("",!0):(n(),l("div",ke,[C(G,{ref_key:"chatInputRef",ref:u,modelValue:i.value,"onUpdate:modelValue":e[2]||(e[2]=a=>i.value=a),placeholder:"Type your health question...","is-loading":m.value,onSend:q,onKeydown:N},null,8,["modelValue","is-loading"])]))])])],2),h.value?(n(),l("div",Ie,[t("div",Ce,[e[25]||(e[25]=t("h3",{class:"text-2xl font-bold text-medroid-navy mb-2"},"Help us personalize your care",-1)),e[26]||(e[26]=t("p",{class:"text-medroid-slate mb-6"},"This information helps us provide better health recommendations.",-1)),t("div",De,[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Age (Optional)",-1)),_(t("select",{"onUpdate:modelValue":e[3]||(e[3]=a=>k.value=a),class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange bg-white"},e[19]||(e[19]=[b('<option value="" data-v-cc9b9133>Select age range</option><option value="18-25" data-v-cc9b9133>18-25</option><option value="26-35" data-v-cc9b9133>26-35</option><option value="36-45" data-v-cc9b9133>36-45</option><option value="46-60" data-v-cc9b9133>46-60</option><option value="61-75" data-v-cc9b9133>61-75</option><option value="75+" data-v-cc9b9133>75+</option>',7)]),512),[[ne,k.value]])]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-medroid-navy mb-3"},"Gender (Optional)",-1)),t("div",_e,[t("label",Te,[_(t("input",{"onUpdate:modelValue":e[4]||(e[4]=a=>v.value=a),type:"radio",value:"male",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[j,v.value]]),e[21]||(e[21]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Male",-1))]),t("label",Me,[_(t("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>v.value=a),type:"radio",value:"female",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[j,v.value]]),e[22]||(e[22]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Female",-1))]),t("label",Se,[_(t("input",{"onUpdate:modelValue":e[6]||(e[6]=a=>v.value=a),type:"radio",value:"other",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[j,v.value]]),e[23]||(e[23]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Other",-1))])])])]),t("div",Ae,[t("button",{onClick:J,class:"flex-1 bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Continue "),t("button",{onClick:e[7]||(e[7]=a=>h.value=!1),class:"px-6 py-3 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"}," Skip ")])])])):f("",!0),w.value?(n(),l("div",Ve,[t("div",je,[e[27]||(e[27]=t("h3",{class:"text-2xl font-bold text-medroid-navy mb-2"},"Continue with an account",-1)),e[28]||(e[28]=t("p",{class:"text-medroid-slate mb-6"},"To book an appointment, please sign in or create an account. Your chat will continue after login.",-1)),t("div",{class:"space-y-3"},[t("button",{onClick:Q,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Sign In "),t("button",{onClick:X,class:"w-full border border-medroid-border text-medroid-navy hover:bg-medroid-sage font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Create Account ")]),t("button",{onClick:e[8]||(e[8]=a=>w.value=!1),class:"w-full mt-4 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"}," Continue without account ")])])):f("",!0),d.value?(n(),l("div",$e,[t("div",ze,[t("div",Be,[C(G,{ref_key:"chatInputRef",ref:u,modelValue:i.value,"onUpdate:modelValue":e[9]||(e[9]=a=>i.value=a),placeholder:"Type your health question...","is-loading":m.value,"show-version":!0,onSend:H,onKeydown:N},null,8,["modelValue","is-loading"])])])])):f("",!0)],2)],64))}}),Ue=re(Le,[["__scopeId","data-v-cc9b9133"]]);export{Ue as default};
