import{c as p}from"./createLucideIcon-BJGbtoZV.js";import{d as l,e as i,f as r,T as m,i as t,n as c,j as n,l as d,t as o,u,P as f,v as _}from"./vendor-bzEMSiaZ.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=p("LoaderCircleIcon",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),h=l({inheritAttrs:!1,__name:"AppLogoIcon",props:{className:{}},setup(a){return(e,s)=>(r(),i("svg",m({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 40 42",class:e.className},e.$attrs),s[0]||(s[0]=[t("path",{fill:"currentColor","fill-rule":"evenodd","clip-rule":"evenodd",d:"M17.2 5.633 8.6.855 0 5.633v26.51l16.2 9 16.2-9v-8.442l7.6-4.223V9.856l-8.6-4.777-8.6 4.777V18.3l-5.6 3.111V5.633ZM38 18.301l-5.6 3.11v-6.157l5.6-3.11V18.3Zm-1.06-7.856-5.54 3.078-5.54-3.079 5.54-3.078 5.54 3.079ZM24.8 18.3v-6.157l5.6 3.111v6.158L24.8 18.3Zm-1 1.732 5.54 3.078-13.14 7.302-5.54-3.078 13.14-7.3v-.002Zm-16.2 7.89 7.6 4.222V38.3L2 30.966V7.92l5.6 3.111v16.892ZM8.6 9.3 3.06 6.222 8.6 3.143l5.54 3.08L8.6 9.3Zm21.8 15.51-13.2 7.334V38.3l13.2-7.334v-6.156ZM9.6 11.034l5.6-3.11v14.6l-5.6 3.11v-14.6Z"},null,-1)]),16))}}),v={class:"flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10"},x={class:"w-full max-w-sm"},g={class:"flex flex-col gap-8"},w={class:"flex flex-col items-center gap-4"},L={class:"mb-1 flex h-9 w-9 items-center justify-center rounded-md"},V={class:"sr-only"},Z={class:"space-y-2 text-center"},y={class:"text-xl font-medium"},k={class:"text-center text-sm text-muted-foreground"},M=l({__name:"AuthSimpleLayout",props:{title:{},description:{}},setup(a){return(e,s)=>(r(),i("div",v,[t("div",x,[t("div",g,[t("div",w,[n(u(f),{href:e.route("home"),class:"flex flex-col items-center gap-2 font-medium"},{default:d(()=>[t("div",L,[n(h,{class:"size-9 fill-current text-[var(--foreground)] dark:text-white"})]),t("span",V,o(e.title),1)]),_:1},8,["href"]),t("div",Z,[t("h1",y,o(e.title),1),t("p",k,o(e.description),1)])]),c(e.$slots,"default")])])]))}}),A=l({__name:"AuthLayout",props:{title:{},description:{}},setup(a){return(e,s)=>(r(),_(M,{title:e.title,description:e.description},{default:d(()=>[c(e.$slots,"default")]),_:3},8,["title","description"]))}});export{$ as L,A as _};
